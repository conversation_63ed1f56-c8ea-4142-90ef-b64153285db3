{
    fmt = {
        __enabled = true,
        __requirestr = "fmt 10",
        linkdirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]],
        version = "10.2.1",
        links = "fmt",
        sysincludedirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]],
        static = true,
        libfiles = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]],
        installdir = [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36]],
        license = "MIT",
        envs = {
            PATH = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\ninja\v1.12.1\dfe0b7329bbc4b29885e425ca7ba22e4\bin]],
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\c\cmake\4.0.3\8bbaf4ce9e114a028062bde15550001d\bin]]
            }
        }
    },
    nlohmann_json = {
        sysincludedirs = [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]],
        __requirestr = "nlohmann_json v3.11.3",
        license = "MIT",
        installdir = [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6]],
        version = "v3.11.3",
        __enabled = true
    }
}