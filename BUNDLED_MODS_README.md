# Bundled Mods Implementation

This document explains the implementation of bundled mods support in LeviLaunchroid to handle dependent shared objects that cannot be loaded through the regular mod loading system due to Android's namespace isolation.

## Problem

The original issue was that two shared objects (`libCrestHelper.so` and `libDynamicLights.so`) have a dependency relationship where `libDynamicLights.so` depends on `libCrestHelper.so`. When using `System.load()` with absolute paths from the cache directory, Android's namespace isolation prevents cross-library dependency resolution, causing `libDynamicLights.so` to fail to load.

Additionally, the DynamicLights mod was causing crashes when loaded too early in the game initialization process, particularly when the player holds a torch in-game.

## Solution

The solution involves bundling the dependent libraries directly into the APK and using `System.loadLibrary()` instead of `System.load()`. This approach allows Android's native library loader to properly resolve dependencies.

To address the crash issue with DynamicLights, the implementation includes:
1. **Safe Loading Mode**: DynamicLights is disabled by default during initial game loading
2. **Delayed Loading**: DynamicLights can be enabled after the game has fully initialized
3. **Crash Protection**: Enhanced error handling and dependency checking

## Implementation Details

### 1. Library Placement
- `libCrestHelper.so` and `libDynamicLights.so` are placed in `app/src/main/jniLibs/arm64-v8a/`
- These libraries are automatically included in the APK during the build process
- Android installs them to the system's native library directory where dependency resolution works properly

### 2. ModBundledLoader Class
A new class `ModBundledLoader` was created to handle bundled mods:

- **Dependency Management**: Maintains a configuration map of libraries and their dependencies
- **Load Order**: Ensures dependencies are loaded before dependent libraries
- **Error Handling**: Continues loading other mods even if one fails
- **Flexibility**: Allows dynamic addition of new bundled mods

### 3. Integration with MinecraftLauncher
The `MinecraftLauncher` was modified to:
1. Load bundled mods first using `ModBundledLoader.loadBundledModsSafely()` (safe mode)
2. Then load regular mods using the existing `ModNativeLoader.loadEnabledSoMods()`

This ensures proper dependency resolution for bundled mods while maintaining compatibility with existing independent mods.

### 4. DynamicLightsManager Class
A new `DynamicLightsManager` class provides:
- **Safe enabling**: `enableDynamicLights()` method to enable DynamicLights after game initialization
- **Status checking**: Methods to check if DynamicLights is enabled or if enable was attempted
- **Delayed loading**: `enableDynamicLightsDelayed()` for timed activation
- **Error recovery**: Reset mechanisms for retry attempts

## Usage

### Current Status
- **CrestHelper**: ✅ Loads automatically and safely during game startup
- **DynamicLights**: ⚠️ Disabled by default due to crash issues when holding torches

### Enabling DynamicLights Safely
To enable DynamicLights after the game has loaded:

```java
// Method 1: Enable immediately (call after game world loads)
boolean success = DynamicLightsManager.enableDynamicLights();

// Method 2: Enable with delay (safer approach)
DynamicLightsManager.enableDynamicLightsDelayed(5000); // 5 second delay

// Check status
String status = DynamicLightsManager.getStatus();
boolean isEnabled = DynamicLightsManager.isDynamicLightsEnabled();
```

### Adding New Bundled Mods
To add new bundled mods with dependencies:

1. Place the `.so` files in `app/src/main/jniLibs/arm64-v8a/`
2. Update the `BUNDLED_MODS_CONFIG` in `ModBundledLoader.java`:
```java
// Example: Adding a new mod that depends on CrestHelper
List<String> newModDeps = new ArrayList<>();
newModDeps.add("CrestHelper");
BUNDLED_MODS_CONFIG.put("NewMod", newModDeps);
```

### Library Naming
- Use the library name without the "lib" prefix and ".so" suffix in configuration
- Example: For `libCrestHelper.so`, use `"CrestHelper"` in the configuration

## Build Process

The build system automatically:
1. Includes libraries from `jniLibs/arm64-v8a/` in the APK
2. Installs them to the appropriate system directory on the device
3. Makes them available for loading via `System.loadLibrary()`

## Testing

Unit tests are provided in `ModBundledLoaderTest.java` to verify:
- Proper recognition of bundled mods
- Correct listing of configured mods
- Dynamic addition of new bundled mods

## Benefits

1. **Dependency Resolution**: Proper handling of dependent libraries
2. **Performance**: Libraries are loaded from system directories (faster than cache)
3. **Reliability**: Uses Android's native library loading mechanism
4. **Maintainability**: Clear separation between bundled and regular mods
5. **Flexibility**: Easy to add new bundled mods as needed

## Troubleshooting

### DynamicLights Crashes
If you experience crashes when holding torches:
1. The current implementation loads only CrestHelper by default
2. DynamicLights is disabled to prevent crashes
3. You can try enabling it manually after the game loads using `DynamicLightsManager.enableDynamicLights()`
4. If crashes persist, the mod may be incompatible with your Minecraft version

### Re-enabling DynamicLights
To re-enable DynamicLights in the automatic loading:
1. Edit `ModBundledLoader.java`
2. Uncomment the DynamicLights configuration in the static block
3. Rebuild the APK

## Files Modified/Created

- `app/src/main/jniLibs/arm64-v8a/libCrestHelper.so` (copied)
- `app/src/main/jniLibs/arm64-v8a/libDynamicLights.so` (copied)
- `app/src/main/java/org/levimc/launcher/core/mods/ModBundledLoader.java` (new)
- `app/src/main/java/org/levimc/launcher/core/mods/DynamicLightsManager.java` (new)
- `app/src/main/java/org/levimc/launcher/core/minecraft/MinecraftLauncher.java` (modified)
- `app/src/test/java/org/levimc/launcher/core/mods/ModBundledLoaderTest.java` (new)
