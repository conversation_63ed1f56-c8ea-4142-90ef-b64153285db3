package org.levimc.launcher.core.mods;

import org.levimc.launcher.util.Logger;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * ModBundledLoader handles loading of shared objects that are bundled directly in the APK.
 * This approach allows proper dependency resolution between libraries by using System.loadLibrary()
 * instead of System.load(), which bypasses Android's namespace isolation issues.
 */
public class ModBundledLoader {
    
    /**
     * Configuration for bundled mods with their dependencies.
     * Key: library name (without "lib" prefix and ".so" suffix)
     * Value: list of dependencies that must be loaded first
     */
    private static final Map<String, List<String>> BUNDLED_MODS_CONFIG = new LinkedHashMap<>();
    
    static {
        // Configure the dependency chain: CrestHelper -> DynamicLights
        // CrestHelper has no dependencies (independent)
        BUNDLED_MODS_CONFIG.put("CrestHelper", new ArrayList<>());

        // DynamicLights depends on CrestHelper
        List<String> dynamicLightsDeps = new ArrayList<>();
        dynamicLightsDeps.add("CrestHelper");
        BUNDLED_MODS_CONFIG.put("DynamicLights", dynamicLightsDeps);
    }
    
    /**
     * Loads all configured bundled mods in the correct dependency order.
     * This method ensures that dependencies are loaded before the libraries that depend on them.
     */
    public static void loadBundledMods() {
        Logger.get().info("Starting to load bundled mods...");

        List<String> loadedLibs = new ArrayList<>();
        List<String> failedLibs = new ArrayList<>();

        for (Map.Entry<String, List<String>> entry : BUNDLED_MODS_CONFIG.entrySet()) {
            String libName = entry.getKey();
            List<String> dependencies = entry.getValue();

            try {
                // Check if all dependencies are loaded
                boolean canLoad = true;
                for (String dep : dependencies) {
                    if (!loadedLibs.contains(dep)) {
                        Logger.get().error("Cannot load " + libName + ": dependency " + dep + " not loaded yet");
                        canLoad = false;
                        break;
                    }
                }

                if (canLoad) {
                    Logger.get().info("Attempting to load bundled mod: lib" + libName + ".so");

                    // Add a small delay to ensure proper initialization order
                    if (!loadedLibs.isEmpty()) {
                        try {
                            Thread.sleep(200); // 200ms delay between library loads for stability
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }

                    // Special handling for DynamicLights to prevent crashes
                    if ("DynamicLights".equals(libName)) {
                        Logger.get().info("Loading DynamicLights with extra safety measures...");
                        try {
                            Thread.sleep(500); // Extra delay for DynamicLights
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }

                    // Load the library using System.loadLibrary (without lib prefix and .so suffix)
                    System.loadLibrary(libName);
                    loadedLibs.add(libName);
                    Logger.get().info("Successfully loaded bundled mod: lib" + libName + ".so");

                    // Additional verification that the library was loaded correctly
                    Logger.get().info("Library " + libName + " loaded and initialized successfully");

                } else {
                    Logger.get().error("Skipping " + libName + " due to unmet dependencies");
                    failedLibs.add(libName);
                }

            } catch (UnsatisfiedLinkError e) {
                Logger.get().error("Failed to load bundled mod lib" + libName + ".so: " + e.getMessage());
                Logger.get().error("UnsatisfiedLinkError details: " + e.toString());
                failedLibs.add(libName);
                // Continue loading other mods even if one fails
            } catch (Exception e) {
                Logger.get().error("Unexpected error loading bundled mod lib" + libName + ".so: " + e.getMessage());
                Logger.get().error("Exception details: " + e.toString());
                failedLibs.add(libName);
            }
        }

        Logger.get().info("Finished loading bundled mods. Loaded " + loadedLibs.size() + " libraries successfully.");
        if (!failedLibs.isEmpty()) {
            Logger.get().warn("Failed to load " + failedLibs.size() + " libraries: " + failedLibs);
        }
    }
    
    /**
     * Checks if a library is configured as a bundled mod.
     * @param libName The library name (with or without lib prefix and .so suffix)
     * @return true if the library is configured as bundled
     */
    public static boolean isBundledMod(String libName) {
        // Normalize the library name to remove lib prefix and .so suffix
        String normalizedName = normalizeName(libName);
        return BUNDLED_MODS_CONFIG.containsKey(normalizedName);
    }
    
    /**
     * Gets the list of configured bundled mod names.
     * @return List of bundled mod names (without lib prefix and .so suffix)
     */
    public static List<String> getBundledModNames() {
        return new ArrayList<>(BUNDLED_MODS_CONFIG.keySet());
    }
    
    /**
     * Normalizes a library name by removing "lib" prefix and ".so" suffix if present.
     * @param libName The library name to normalize
     * @return The normalized name
     */
    private static String normalizeName(String libName) {
        String normalized = libName;
        if (normalized.startsWith("lib")) {
            normalized = normalized.substring(3);
        }
        if (normalized.endsWith(".so")) {
            normalized = normalized.substring(0, normalized.length() - 3);
        }
        return normalized;
    }
    
    /**
     * Safely enables DynamicLights mod if CrestHelper is loaded.
     * This method should be called after the game has fully initialized.
     */
    public static void enableDynamicLightsIfSafe() {
        if (isBundledMod("CrestHelper") && !isBundledMod("DynamicLights")) {
            try {
                Logger.get().info("Attempting to safely enable DynamicLights mod...");
                List<String> dynamicLightsDeps = new ArrayList<>();
                dynamicLightsDeps.add("CrestHelper");
                BUNDLED_MODS_CONFIG.put("DynamicLights", dynamicLightsDeps);

                // Try to load DynamicLights
                System.loadLibrary("DynamicLights");
                Logger.get().info("Successfully enabled DynamicLights mod");
            } catch (UnsatisfiedLinkError e) {
                Logger.get().error("Failed to enable DynamicLights mod: " + e.getMessage());
                // Remove from config if loading failed
                BUNDLED_MODS_CONFIG.remove("DynamicLights");
            } catch (Exception e) {
                Logger.get().error("Unexpected error enabling DynamicLights mod: " + e.getMessage());
                // Remove from config if loading failed
                BUNDLED_MODS_CONFIG.remove("DynamicLights");
            }
        }
    }

    /**
     * Loads bundled mods with experimental features disabled by default.
     * Call enableDynamicLightsIfSafe() later if you want to enable DynamicLights.
     */
    public static void loadBundledModsSafely() {
        Logger.get().info("Loading bundled mods in safe mode (experimental features disabled)...");
        loadBundledMods();
    }

    /**
     * Adds a new bundled mod configuration.
     * This method can be used to dynamically add more bundled mods if needed.
     * @param libName The library name (without lib prefix and .so suffix)
     * @param dependencies List of dependency names that must be loaded first
     */
    public static void addBundledMod(String libName, List<String> dependencies) {
        String normalizedName = normalizeName(libName);
        BUNDLED_MODS_CONFIG.put(normalizedName, new ArrayList<>(dependencies));
        try {
            Logger.get().info("Added bundled mod configuration: " + normalizedName + " with dependencies: " + dependencies);
        } catch (Exception e) {
            // Ignore logging errors (e.g., in unit test environment)
            System.out.println("Added bundled mod configuration: " + normalizedName + " with dependencies: " + dependencies);
        }
    }
}
