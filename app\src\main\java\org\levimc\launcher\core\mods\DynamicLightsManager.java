package org.levimc.launcher.core.mods;

import org.levimc.launcher.util.Logger;

/**
 * DynamicLightsManager handles the safe loading and management of the DynamicLights mod.
 * This class provides methods to enable DynamicLights after the game has fully initialized,
 * which helps prevent crashes that can occur when the mod is loaded too early.
 */
public class DynamicLightsManager {
    
    private static boolean dynamicLightsEnabled = false;
    private static boolean enableAttempted = false;
    
    /**
     * Attempts to enable DynamicLights mod safely.
     * This should be called after the game world has loaded and the player is in-game.
     * 
     * @return true if DynamicLights was successfully enabled, false otherwise
     */
    public static boolean enableDynamicLights() {
        if (dynamicLightsEnabled) {
            Logger.get().info("DynamicLights is already enabled");
            return true;
        }
        
        if (enableAttempted) {
            Logger.get().warn("DynamicLights enable was already attempted and failed");
            return false;
        }
        
        enableAttempted = true;
        
        try {
            Logger.get().info("Attempting to enable DynamicLights mod...");
            
            // Check if CrestHelper is available (dependency)
            if (!ModBundledLoader.isBundledMod("CrestHelper")) {
                Logger.get().error("Cannot enable DynamicLights: CrestHelper dependency not found");
                return false;
            }
            
            // Enable DynamicLights through the bundled loader
            ModBundledLoader.enableDynamicLightsIfSafe();
            
            // Verify that it was loaded successfully
            if (ModBundledLoader.isBundledMod("DynamicLights")) {
                dynamicLightsEnabled = true;
                Logger.get().info("DynamicLights mod enabled successfully!");
                return true;
            } else {
                Logger.get().error("DynamicLights mod failed to load");
                return false;
            }
            
        } catch (Exception e) {
            Logger.get().error("Failed to enable DynamicLights mod: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Checks if DynamicLights mod is currently enabled.
     * 
     * @return true if DynamicLights is enabled, false otherwise
     */
    public static boolean isDynamicLightsEnabled() {
        return dynamicLightsEnabled;
    }
    
    /**
     * Checks if an attempt to enable DynamicLights has been made.
     * 
     * @return true if enable was attempted, false otherwise
     */
    public static boolean wasEnableAttempted() {
        return enableAttempted;
    }
    
    /**
     * Resets the enable attempt flag. Use this if you want to retry enabling DynamicLights.
     * This should only be used in special circumstances, such as after a game restart.
     */
    public static void resetEnableAttempt() {
        enableAttempted = false;
        Logger.get().info("DynamicLights enable attempt flag reset");
    }
    
    /**
     * Provides status information about DynamicLights mod.
     * 
     * @return A string describing the current status of DynamicLights
     */
    public static String getStatus() {
        if (dynamicLightsEnabled) {
            return "DynamicLights: ENABLED";
        } else if (enableAttempted) {
            return "DynamicLights: FAILED TO ENABLE";
        } else {
            return "DynamicLights: NOT ATTEMPTED";
        }
    }
    
    /**
     * Attempts to enable DynamicLights with a delay.
     * This method runs the enable attempt in a background thread after a specified delay.
     * 
     * @param delayMs Delay in milliseconds before attempting to enable
     */
    public static void enableDynamicLightsDelayed(long delayMs) {
        new Thread(() -> {
            try {
                Thread.sleep(delayMs);
                enableDynamicLights();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                Logger.get().warn("DynamicLights delayed enable was interrupted");
            }
        }).start();
    }
}
