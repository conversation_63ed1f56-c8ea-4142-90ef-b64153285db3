{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-Xcuda-fatbinary"] = true,
            ["-Tbss"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fasync-exceptions"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["--emit-static-lib"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-Xanalyzer"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-gdwarf-2"] = true,
            ["-g"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fgpu-rdc"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-ffixed-x29"] = true,
            ["-fmodules-search-all"] = true,
            ["-fuse-line-directives"] = true,
            ["-gline-tables-only"] = true,
            ["-arch"] = true,
            ["-G"] = true,
            ["-fpascal-strings"] = true,
            ["-mcrc"] = true,
            ["-dsym-dir"] = true,
            ["-freciprocal-math"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fno-spell-checking"] = true,
            ["-emit-ast"] = true,
            ["-working-directory"] = true,
            ["-MF"] = true,
            ["-mlong-double-80"] = true,
            ["-fmemory-profile"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fstack-protector"] = true,
            ["-ffixed-x7"] = true,
            ["-mno-crc"] = true,
            ["-MMD"] = true,
            ["-gdwarf-4"] = true,
            ["-ffinite-loops"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-gdwarf64"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fstrict-enums"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-ffixed-d5"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-E"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-iprefix"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-miamcu"] = true,
            ["-mno-mt"] = true,
            ["-ffixed-x21"] = true,
            ["-fms-compatibility"] = true,
            ["-fdebug-types-section"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-dM"] = true,
            ["--help-hidden"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-F"] = true,
            ["-fverbose-asm"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fblocks"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fno-autolink"] = true,
            ["-mno-nvj"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-ffixed-r19"] = true,
            ["-ffixed-x16"] = true,
            ["-fcs-profile-generate"] = true,
            ["-Xlinker"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-ffixed-x9"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fsycl"] = true,
            ["-fshort-wchar"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fno-operator-names"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-mhvx"] = true,
            ["-fconvergent-functions"] = true,
            ["-ffixed-a1"] = true,
            ["-fno-split-stack"] = true,
            ["-dependency-dot"] = true,
            ["-L"] = true,
            ["-ffixed-x2"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-mllvm"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["--precompile"] = true,
            ["-fno-show-source-location"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-fjump-tables"] = true,
            ["--hip-link"] = true,
            ["-fno-plt"] = true,
            ["-index-header-map"] = true,
            ["-static-openmp"] = true,
            ["-fgnu-runtime"] = true,
            ["-ffixed-x19"] = true,
            ["-fsanitize-trap"] = true,
            ["-mno-packets"] = true,
            ["-I"] = true,
            ["-ffixed-x25"] = true,
            ["-mno-gpopt"] = true,
            ["-Wdeprecated"] = true,
            ["--verify-debug-info"] = true,
            ["-munaligned-access"] = true,
            ["-fapprox-func"] = true,
            ["-fno-integrated-as"] = true,
            ["-fborland-extensions"] = true,
            ["-dI"] = true,
            ["-ffixed-d1"] = true,
            ["-fno-strict-return"] = true,
            ["-ffixed-x14"] = true,
            ["-fno-elide-type"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-ivfsoverlay"] = true,
            ["-help"] = true,
            ["-mmadd4"] = true,
            ["-ffixed-x8"] = true,
            ["-ffixed-x10"] = true,
            ["-pthread"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-MV"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-cl-mad-enable"] = true,
            ["-fgnu89-inline"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fopenmp"] = true,
            ["--config"] = true,
            ["-print-resource-dir"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fno-unique-section-names"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-faligned-allocation"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fdigraphs"] = true,
            ["-finline-hint-functions"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fcxx-modules"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fslp-vectorize"] = true,
            ["-mno-abicalls"] = true,
            ["-fmodules-decluse"] = true,
            ["-fcf-protection"] = true,
            ["-fno-exceptions"] = true,
            ["-dependency-file"] = true,
            ["-fdeclspec"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fprotect-parens"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-relocatable-pch"] = true,
            ["-ffixed-d3"] = true,
            ["-fno-show-column"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-Tdata"] = true,
            ["-mrestrict-it"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-mnocrc"] = true,
            ["-fno-signed-zeros"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-ffixed-d4"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-mno-tgsplit"] = true,
            ["-fno-new-infallible"] = true,
            ["-ffixed-a0"] = true,
            ["-isystem-after"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-ffixed-point"] = true,
            ["-fno-profile-generate"] = true,
            ["-ffreestanding"] = true,
            ["-MQ"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-iwithsysroot"] = true,
            ["-mpackets"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-trigraphs"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fstandalone-debug"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-mrelax"] = true,
            ["-foffload-lto"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-isysroot"] = true,
            ["-ffast-math"] = true,
            ["-MD"] = true,
            ["-mmsa"] = true,
            ["-ffixed-x17"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fno-discard-value-names"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["--no-cuda-version-check"] = true,
            ["-D"] = true,
            ["-emit-llvm"] = true,
            ["-ffixed-d7"] = true,
            ["-mno-unaligned-access"] = true,
            ["-mbackchain"] = true,
            ["-include-pch"] = true,
            ["-fsized-deallocation"] = true,
            ["-shared-libsan"] = true,
            ["-ffixed-x3"] = true,
            ["-gembed-source"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fstack-clash-protection"] = true,
            ["-print-targets"] = true,
            ["-msvr4-struct-return"] = true,
            ["-gdwarf-5"] = true,
            ["-nogpulib"] = true,
            ["-mcode-object-v3"] = true,
            ["-Ttext"] = true,
            ["-mlong-double-128"] = true,
            ["-mamdgpu-ieee"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fsystem-module"] = true,
            ["-fcall-saved-x15"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-Qy"] = true,
            ["-fenable-matrix"] = true,
            ["-mno-nvs"] = true,
            ["-flto"] = true,
            ["-mno-memops"] = true,
            ["-c"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fno-debug-macro"] = true,
            ["-mmemops"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-v"] = true,
            ["-fprofile-generate"] = true,
            ["-fshort-enums"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-offload-lto"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-time"] = true,
            ["-print-multiarch"] = true,
            ["-ffixed-d6"] = true,
            ["-gdwarf"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-ffixed-x5"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fpch-codegen"] = true,
            ["-ibuiltininc"] = true,
            ["-fno-declspec"] = true,
            ["-Xassembler"] = true,
            ["-emit-interface-stubs"] = true,
            ["-U"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-gdwarf-3"] = true,
            ["--analyze"] = true,
            ["-x"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fno-global-isel"] = true,
            ["-fno-fixed-point"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-dD"] = true,
            ["-mglobal-merge"] = true,
            ["--analyzer-output"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-finstrument-functions"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fopenmp-simd"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ftrapv"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-finline-functions"] = true,
            ["-freg-struct-return"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fno-use-init-array"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-ffixed-a4"] = true,
            ["-Xpreprocessor"] = true,
            ["-gno-embed-source"] = true,
            ["-fno-finite-loops"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fms-extensions"] = true,
            ["-I-"] = true,
            ["-msoft-float"] = true,
            ["-fno-memory-profile"] = true,
            ["-ffixed-x26"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-maix-struct-return"] = true,
            ["-mlvi-cfi"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-pedantic"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fmodules"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-mpacked-stack"] = true,
            ["-w"] = true,
            ["-ffixed-a6"] = true,
            ["-mnvj"] = true,
            ["-Qunused-arguments"] = true,
            ["-mno-restrict-it"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mnop-mcount"] = true,
            ["-fmath-errno"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-ftime-trace"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-mrelax-all"] = true,
            ["-mno-relax"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fobjc-arc"] = true,
            ["-fcall-saved-x13"] = true,
            ["-pg"] = true,
            ["-MJ"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-traditional-cpp"] = true,
            ["-MM"] = true,
            ["-print-supported-cpus"] = true,
            ["-mno-long-calls"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-funroll-loops"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-nogpuinc"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-nobuiltininc"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fopenmp-extensions"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-print-ivar-layout"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-mmt"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-fno-jump-tables"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-mms-bitfields"] = true,
            ["-fforce-enable-int128"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-rtti-data"] = true,
            ["-verify-pch"] = true,
            ["-fcall-saved-x18"] = true,
            ["-ffunction-sections"] = true,
            ["-ffixed-x1"] = true,
            ["-faddrsig"] = true,
            ["-fembed-bitcode"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-mno-outline"] = true,
            ["-ffixed-x28"] = true,
            ["-save-temps"] = true,
            ["-fno-access-control"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fdata-sections"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-b"] = true,
            ["-fpch-debuginfo"] = true,
            ["-gdwarf32"] = true,
            ["--cuda-host-only"] = true,
            ["-mno-global-merge"] = true,
            ["-T"] = true,
            ["-mstack-arg-probe"] = true,
            ["-module-dependency-dir"] = true,
            ["-fno-sycl"] = true,
            ["-print-runtime-dir"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-ffixed-x13"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-mno-embedded-data"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-mmark-bti-property"] = true,
            ["-fobjc-weak"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-print-effective-triple"] = true,
            ["-fstack-usage"] = true,
            ["-fno-trigraphs"] = true,
            ["-fgnu-keywords"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fsplit-stack"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-ffixed-d0"] = true,
            ["-ffixed-x18"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-ffixed-x12"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fno-temp-file"] = true,
            ["-fxray-link-deps"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mseses"] = true,
            ["-fansi-escape-codes"] = true,
            ["-mfentry"] = true,
            ["-ffixed-a3"] = true,
            ["-fstack-protector-all"] = true,
            ["-undef"] = true,
            ["-ftrigraphs"] = true,
            ["-rewrite-objc"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-mfp64"] = true,
            ["-fcommon"] = true,
            ["-ffixed-x6"] = true,
            ["-mlong-calls"] = true,
            ["-mgpopt"] = true,
            ["-ffixed-x24"] = true,
            ["-isystem"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-cxx-isystem"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-z"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-xray-function-index"] = true,
            ["-print-target-triple"] = true,
            ["-idirafter"] = true,
            ["-iquote"] = true,
            ["-save-stats"] = true,
            ["-mtgsplit"] = true,
            ["-ffixed-d2"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fno-digraphs"] = true,
            ["-print-search-dirs"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mno-movt"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-mibt-seal"] = true,
            ["-ffixed-x15"] = true,
            ["-fignore-exceptions"] = true,
            ["-mno-msa"] = true,
            ["-fnew-infallible"] = true,
            ["-fno-short-wchar"] = true,
            ["-module-file-info"] = true,
            ["-cl-opt-disable"] = true,
            ["-rpath"] = true,
            ["-mlocal-sdata"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fwritable-strings"] = true,
            ["-mno-implicit-float"] = true,
            ["-fdebug-macro"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-mexecute-only"] = true,
            ["-mabicalls"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-ffixed-x31"] = true,
            ["--cuda-device-only"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fexceptions"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fintegrated-as"] = true,
            ["-mcumode"] = true,
            ["-femit-all-decls"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-gcodeview"] = true,
            ["-fsanitize-stats"] = true,
            ["-fno-lto"] = true,
            ["-ffixed-x27"] = true,
            ["-mstackrealign"] = true,
            ["-fseh-exceptions"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-elide-constructors"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-extract-api"] = true,
            ["-Xopenmp-target"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-pipe"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fno-builtin"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-o"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["--version"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-M"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fno-stack-protector"] = true,
            ["-fapple-kext"] = true,
            ["-fstack-size-section"] = true,
            ["-P"] = true,
            ["-serialize-diagnostics"] = true,
            ["-gline-directives-only"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fcall-saved-x11"] = true,
            ["--migrate"] = true,
            ["-mrecord-mcount"] = true,
            ["-membedded-data"] = true,
            ["-mfp32"] = true,
            ["-nostdinc"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-ffixed-x23"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-mextern-sdata"] = true,
            ["-Xclang"] = true,
            ["-ffixed-x20"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-imacros"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-iwithprefix"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-MT"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-frwpi"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-B"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fxray-instrument"] = true,
            ["-ffixed-x4"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fsigned-char"] = true,
            ["-mlong-double-64"] = true,
            ["-mno-cumode"] = true,
            ["-msave-restore"] = true,
            ["-gmodules"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fcall-saved-x12"] = true,
            ["-mno-save-restore"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-C"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fno-rtti"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-S"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fno-cxx-modules"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-mno-execute-only"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-mno-madd4"] = true,
            ["-MG"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fcall-saved-x8"] = true,
            ["-meabi"] = true,
            ["-ffixed-x11"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-mno-hvx"] = true,
            ["-fno-signed-char"] = true,
            ["-moutline"] = true,
            ["-ffixed-x30"] = true,
            ["-CC"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-mno-seses"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fno-addrsig"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mcmse"] = true,
            ["-fmodules-ts"] = true,
            ["-mrtd"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-MP"] = true,
            ["-fapplication-extension"] = true,
            ["-include"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-Qn"] = true,
            ["-fno-unroll-loops"] = true,
            ["-freroll-loops"] = true,
            ["-femulated-tls"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-emit-module"] = true,
            ["-mnvs"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-ffixed-x22"] = true,
            ["-ffixed-a2"] = true,
            ["-mlvi-hardening"] = true,
            ["-ffixed-r9"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-mthread-model"] = true,
            ["-fms-hotpatch"] = true,
            ["-malign-double"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fvectorize"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-moutline-atomics"] = true,
            ["-fropi"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fno-common"] = true,
            ["-fzvector"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-static-libsan"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-H"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mhvx-qfloat"] = true
        }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            ndkver = 25,
            cross = "arm-linux-androideabi-",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sdkver = "21"
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true
    },
    ["find_program_utils.binary.deplibs"] = {
        objdump = [[C:\msys64\usr\bin\objdump.exe]],
        ["llvm-objdump"] = false
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--export-all-symbols"] = true,
            ["--gc-sections"] = true,
            ["--insert-timestamp"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--dynamicbase"] = true,
            ["--strip-debug"] = true,
            ["-dn"] = true,
            ["--no-whole-archive"] = true,
            ["--disable-auto-import"] = true,
            ["-L"] = true,
            ["--high-entropy-va"] = true,
            ["--no-gc-sections"] = true,
            ["-o"] = true,
            ["-m"] = true,
            ["--kill-at"] = true,
            ["-s"] = true,
            ["--appcontainer"] = true,
            ["--version"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--help"] = true,
            ["--whole-archive"] = true,
            ["--disable-no-seh"] = true,
            ["--Bdynamic"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--disable-dynamicbase"] = true,
            ["--strip-all"] = true,
            ["-S"] = true,
            ["-static"] = true,
            ["--demangle"] = true,
            ["--disable-tsaware"] = true,
            ["--exclude-all-symbols"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--verbose"] = true,
            ["--no-insert-timestamp"] = true,
            ["--no-demangle"] = true,
            ["--shared"] = true,
            ["--allow-multiple-definition"] = true,
            ["--enable-auto-import"] = true,
            ["--fatal-warnings"] = true,
            ["--no-fatal-warnings"] = true,
            ["--disable-nxcompat"] = true,
            ["--nxcompat"] = true,
            ["--Bstatic"] = true,
            ["-v"] = true,
            ["--disable-high-entropy-va"] = true,
            ["-l"] = true,
            ["-dy"] = true,
            ["--large-address-aware"] = true,
            ["--no-seh"] = true,
            ["--tsaware"] = true,
            ["--no-dynamicbase"] = true,
            ["--disable-runtime-pseudo-reloc"] = true
        }
    }
}