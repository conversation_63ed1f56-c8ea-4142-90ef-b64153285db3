{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Wno-gnu-line-marker -Werror"] = false,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-MMD -MF"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            ndkver = 25,
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            cross = "arm-linux-androideabi-",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sdkver = "21",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-ffixed-x18"] = true,
            ["-faligned-allocation"] = true,
            ["-fxray-instrument"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-MP"] = true,
            ["-print-supported-cpus"] = true,
            ["-ffixed-d5"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-mno-long-calls"] = true,
            ["-fno-rtti-data"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fdebug-types-section"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-B"] = true,
            ["-gline-directives-only"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-Xassembler"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-mpackets"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-gdwarf-5"] = true,
            ["-fcommon"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-ftime-trace"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-emit-llvm"] = true,
            ["-isystem-after"] = true,
            ["-Wdeprecated"] = true,
            ["-ffixed-x11"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fmodules"] = true,
            ["-fmath-errno"] = true,
            ["-mnocrc"] = true,
            ["-mfentry"] = true,
            ["-fcall-saved-x12"] = true,
            ["-Ttext"] = true,
            ["-b"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-flto"] = true,
            ["-MV"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-mrelax"] = true,
            ["-ffixed-d0"] = true,
            ["-mlvi-hardening"] = true,
            ["--config"] = true,
            ["-fmodules-decluse"] = true,
            ["-fembed-bitcode"] = true,
            ["-MF"] = true,
            ["-arch"] = true,
            ["-maix-struct-return"] = true,
            ["-fms-hotpatch"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["--emit-static-lib"] = true,
            ["-working-directory"] = true,
            ["-cxx-isystem"] = true,
            ["-fno-stack-protector"] = true,
            ["-print-ivar-layout"] = true,
            ["-fno-discard-value-names"] = true,
            ["-mseses"] = true,
            ["-fno-show-source-location"] = true,
            ["-fmodules-ts"] = true,
            ["-verify-pch"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-moutline-atomics"] = true,
            ["-fstandalone-debug"] = true,
            ["-fborland-extensions"] = true,
            ["-ftrigraphs"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-print-resource-dir"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-mno-outline-atomics"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-freg-struct-return"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-funroll-loops"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-save-temps"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-mno-implicit-float"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-ffixed-x6"] = true,
            ["-gno-embed-source"] = true,
            ["-foffload-lto"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-ffunction-sections"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-MT"] = true,
            ["-fstrict-enums"] = true,
            ["-fgnu-runtime"] = true,
            ["-dependency-file"] = true,
            ["-membedded-data"] = true,
            ["--analyze"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fcf-protection"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-short-wchar"] = true,
            ["-mstackrealign"] = true,
            ["-ffixed-x29"] = true,
            ["-gmodules"] = true,
            ["-relocatable-pch"] = true,
            ["-fpascal-strings"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-ffixed-a1"] = true,
            ["-c"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-extract-api"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-mfp64"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-ffixed-d2"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fno-plt"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-ffixed-x8"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fsized-deallocation"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fno-temp-file"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fmemory-profile"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fapple-kext"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-MQ"] = true,
            ["-fxray-link-deps"] = true,
            ["-ffixed-x21"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-femulated-tls"] = true,
            ["-Xclang"] = true,
            ["-w"] = true,
            ["-mnvj"] = true,
            ["-mms-bitfields"] = true,
            ["-ffixed-d7"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fsystem-module"] = true,
            ["-mcrc"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-signed-zeros"] = true,
            ["-mlong-double-64"] = true,
            ["-mmsa"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fno-digraphs"] = true,
            ["-MM"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-Xpreprocessor"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fshort-wchar"] = true,
            ["-fuse-line-directives"] = true,
            ["-fno-rtti"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-ffixed-d6"] = true,
            ["-z"] = true,
            ["--end-no-unused-arguments"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-cl-no-stdinc"] = true,
            ["-gdwarf64"] = true,
            ["-mno-nvs"] = true,
            ["-fcall-saved-x9"] = true,
            ["-ffixed-x26"] = true,
            ["-fno-common"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fdata-sections"] = true,
            ["-ffast-math"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-mgpopt"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fno-finite-loops"] = true,
            ["-ffixed-x5"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-signed-char"] = true,
            ["-fropi"] = true,
            ["-mno-hvx"] = true,
            ["-ffixed-a3"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-mnop-mcount"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fsycl"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-include"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-Xanalyzer"] = true,
            ["-emit-interface-stubs"] = true,
            ["-gdwarf-2"] = true,
            ["-fgpu-rdc"] = true,
            ["-MG"] = true,
            ["-fno-jump-tables"] = true,
            ["-mno-msa"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-mhvx-qfloat"] = true,
            ["-mfp32"] = true,
            ["-fno-exceptions"] = true,
            ["-dsym-dir"] = true,
            ["-iquote"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-msave-restore"] = true,
            ["-Qy"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-ffixed-x27"] = true,
            ["-mlong-calls"] = true,
            ["-ffixed-x2"] = true,
            ["-fenable-matrix"] = true,
            ["-gembed-source"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-MMD"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fconvergent-functions"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-operator-names"] = true,
            ["-mno-cumode"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-mglobal-merge"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fcxx-modules"] = true,
            ["-fcall-saved-x13"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fshort-enums"] = true,
            ["-rpath"] = true,
            ["-mcode-object-v3"] = true,
            ["-mno-relax"] = true,
            ["-finline-functions"] = true,
            ["-print-multiarch"] = true,
            ["-dM"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-mno-execute-only"] = true,
            ["-ffixed-x4"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-autolink"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-trigraphs"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fzvector"] = true,
            ["-fgnu-keywords"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fverbose-asm"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mbackchain"] = true,
            ["-faddrsig"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fdebug-macro"] = true,
            ["-mno-local-sdata"] = true,
            ["-fcs-profile-generate"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-dI"] = true,
            ["-nobuiltininc"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fno-global-isel"] = true,
            ["-ffixed-x24"] = true,
            ["-ffixed-point"] = true,
            ["-nogpuinc"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-mmark-bti-property"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-static-libsan"] = true,
            ["-dependency-dot"] = true,
            ["-fglobal-isel"] = true,
            ["-isystem"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fno-lto"] = true,
            ["-o"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fno-spell-checking"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-a6"] = true,
            ["-emit-module"] = true,
            ["-fno-fixed-point"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fprotect-parens"] = true,
            ["-ffixed-x25"] = true,
            ["-ffixed-d4"] = true,
            ["-fvectorize"] = true,
            ["-mllvm"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fno-elide-type"] = true,
            ["-mabicalls"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-pipe"] = true,
            ["-ffixed-d1"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-mstack-arg-probe"] = true,
            ["-U"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-standalone-debug"] = true,
            ["-malign-double"] = true,
            ["-fnew-infallible"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fobjc-arc"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-traditional-cpp"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fcall-saved-x10"] = true,
            ["-mcumode"] = true,
            ["-fslp-vectorize"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fapplication-extension"] = true,
            ["--hip-link"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-mexecute-only"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-pthread"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fasync-exceptions"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fstack-protector"] = true,
            ["-fno-offload-lto"] = true,
            ["-freroll-loops"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-isysroot"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-gline-tables-only"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fstack-protector-all"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-integrated-as"] = true,
            ["-fno-strict-return"] = true,
            ["-help"] = true,
            ["-fno-builtin"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fcall-saved-x11"] = true,
            ["-mno-outline"] = true,
            ["-fdigraphs"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-mno-gpopt"] = true,
            ["-S"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fstack-size-section"] = true,
            ["-ffixed-x7"] = true,
            ["-Qn"] = true,
            ["-print-targets"] = true,
            ["-fno-declspec"] = true,
            ["-MJ"] = true,
            ["-fdeclspec"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fopenmp-simd"] = true,
            ["-fpch-debuginfo"] = true,
            ["-include-pch"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-T"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-ffixed-x10"] = true,
            ["-ffixed-x17"] = true,
            ["-print-runtime-dir"] = true,
            ["-ffixed-x9"] = true,
            ["-fno-access-control"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-mno-crc"] = true,
            ["-x"] = true,
            ["-fpcc-struct-return"] = true,
            ["-Xlinker"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-nogpulib"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-ffixed-x1"] = true,
            ["-ffixed-x22"] = true,
            ["-mmt"] = true,
            ["-v"] = true,
            ["-static-openmp"] = true,
            ["-fno-profile-generate"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-mtgsplit"] = true,
            ["-mno-global-merge"] = true,
            ["-fsplit-machine-functions"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fopenmp"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-mno-restrict-it"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fpch-codegen"] = true,
            ["-fno-pch-codegen"] = true,
            ["-gdwarf-3"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-E"] = true,
            ["-MD"] = true,
            ["-fms-extensions"] = true,
            ["-ffixed-x23"] = true,
            ["-mno-memops"] = true,
            ["-mthread-model"] = true,
            ["-mlocal-sdata"] = true,
            ["-pg"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-time"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-emit-merged-ifs"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fprofile-generate"] = true,
            ["-ffixed-x3"] = true,
            ["-ffixed-x12"] = true,
            ["-F"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-ffixed-a2"] = true,
            ["--precompile"] = true,
            ["-iprefix"] = true,
            ["-pedantic"] = true,
            ["-gdwarf32"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fcoverage-mapping"] = true,
            ["-mcmse"] = true,
            ["-Xopenmp-target"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-miamcu"] = true,
            ["-I"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fmodules-search-all"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-split-stack"] = true,
            ["-mibt-seal"] = true,
            ["-index-header-map"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fsanitize-trap"] = true,
            ["-gcodeview"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-ffinite-loops"] = true,
            ["-fcall-saved-x15"] = true,
            ["-mmemops"] = true,
            ["-msvr4-struct-return"] = true,
            ["-iwithsysroot"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-ffixed-x28"] = true,
            ["-ffreestanding"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-g"] = true,
            ["-dD"] = true,
            ["-ffixed-x20"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fno-show-column"] = true,
            ["-ffixed-x31"] = true,
            ["-ffixed-a4"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-D"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fcall-saved-x8"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fsigned-char"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-ffixed-r9"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-idirafter"] = true,
            ["-munaligned-access"] = true,
            ["-C"] = true,
            ["-fno-trigraphs"] = true,
            ["-fno-cxx-modules"] = true,
            ["-H"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-ffixed-r19"] = true,
            ["-fstack-usage"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fansi-escape-codes"] = true,
            ["-ffixed-x19"] = true,
            ["-ffixed-x14"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fdirect-access-external-data"] = true,
            ["--version"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-gdwarf-4"] = true,
            ["-mrtd"] = true,
            ["-mno-mt"] = true,
            ["-objcmt-migrate-all"] = true,
            ["--analyzer-output"] = true,
            ["-fno-addrsig"] = true,
            ["-fsplit-stack"] = true,
            ["-mextern-sdata"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-imacros"] = true,
            ["-Tbss"] = true,
            ["-meabi"] = true,
            ["-module-dependency-dir"] = true,
            ["-mhvx"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-use-init-array"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-ibuiltininc"] = true,
            ["-G"] = true,
            ["-mmadd4"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fno-new-infallible"] = true,
            ["-fforce-enable-int128"] = true,
            ["-ffixed-d3"] = true,
            ["-freciprocal-math"] = true,
            ["-femit-all-decls"] = true,
            ["-finstrument-functions"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-mno-embedded-data"] = true,
            ["-module-file-info"] = true,
            ["--migrate"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-ftrapv"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-iwithprefix"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fobjc-weak"] = true,
            ["-finline-hint-functions"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-moutline"] = true,
            ["-fno-debug-macro"] = true,
            ["-print-effective-triple"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-I-"] = true,
            ["-mrestrict-it"] = true,
            ["-shared-libsan"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fno-elide-constructors"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fapprox-func"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-mno-movt"] = true,
            ["-fno-sycl"] = true,
            ["-P"] = true,
            ["-save-stats"] = true,
            ["-cl-finite-math-only"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fopenmp-extensions"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fms-compatibility"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fignore-exceptions"] = true,
            ["-ffixed-x15"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fno-sanitize-trap"] = true,
            ["--help-hidden"] = true,
            ["-fsanitize-stats"] = true,
            ["-M"] = true,
            ["-msoft-float"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-mrecord-mcount"] = true,
            ["-CC"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-cl-opt-disable"] = true,
            ["-fgnu89-inline"] = true,
            ["-fjump-tables"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-mno-madd4"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ffixed-x30"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-mnvs"] = true,
            ["-ffixed-a0"] = true,
            ["--verify-debug-info"] = true,
            ["-fseh-exceptions"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-ffixed-x13"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-mno-extern-sdata"] = true,
            ["-mpacked-stack"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-L"] = true,
            ["-print-search-dirs"] = true,
            ["-mno-nvj"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-nostdinc"] = true,
            ["-fexceptions"] = true,
            ["-cl-mad-enable"] = true,
            ["--no-cuda-version-check"] = true,
            ["-mno-tgsplit"] = true,
            ["-mno-save-restore"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-print-target-triple"] = true,
            ["-mrelax-all"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-mno-packets"] = true,
            ["-Tdata"] = true,
            ["-gdwarf"] = true,
            ["-mno-seses"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-mlvi-cfi"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mlong-double-128"] = true,
            ["-fintegrated-as"] = true,
            ["-fblocks"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fprofile-instr-generate"] = true,
            ["--cuda-host-only"] = true,
            ["-undef"] = true,
            ["--cuda-device-only"] = true,
            ["-fwasm-exceptions"] = true,
            ["-mno-abicalls"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fminimize-whitespace"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-frwpi"] = true,
            ["-emit-ast"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-mlong-double-80"] = true,
            ["-fdelayed-template-parsing"] = true
        }
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--no-dynamicbase"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--enable-auto-import"] = true,
            ["-S"] = true,
            ["--whole-archive"] = true,
            ["-o"] = true,
            ["--no-demangle"] = true,
            ["-m"] = true,
            ["--strip-debug"] = true,
            ["--tsaware"] = true,
            ["--large-address-aware"] = true,
            ["--exclude-all-symbols"] = true,
            ["--demangle"] = true,
            ["--allow-multiple-definition"] = true,
            ["--disable-auto-import"] = true,
            ["--high-entropy-va"] = true,
            ["--disable-dynamicbase"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--insert-timestamp"] = true,
            ["--kill-at"] = true,
            ["-l"] = true,
            ["--no-fatal-warnings"] = true,
            ["-dn"] = true,
            ["--gc-sections"] = true,
            ["--no-seh"] = true,
            ["--export-all-symbols"] = true,
            ["--disable-nxcompat"] = true,
            ["--disable-no-seh"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["-dy"] = true,
            ["--help"] = true,
            ["--strip-all"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--nxcompat"] = true,
            ["--no-whole-archive"] = true,
            ["-v"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--appcontainer"] = true,
            ["--Bdynamic"] = true,
            ["--version"] = true,
            ["--disable-tsaware"] = true,
            ["--dynamicbase"] = true,
            ["--Bstatic"] = true,
            ["--no-gc-sections"] = true,
            ["-L"] = true,
            ["-static"] = true,
            ["-s"] = true,
            ["--verbose"] = true,
            ["--shared"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--fatal-warnings"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}