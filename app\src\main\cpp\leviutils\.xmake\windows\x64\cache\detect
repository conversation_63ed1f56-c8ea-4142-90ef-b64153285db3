{
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkver = "21",
            ndkver = 25,
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            cross = "arm-linux-androideabi-"
        }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-mbackchain"] = true,
            ["-fmodules"] = true,
            ["-print-multiarch"] = true,
            ["-mno-seses"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-operator-names"] = true,
            ["-gdwarf"] = true,
            ["-trigraphs"] = true,
            ["-mstackrealign"] = true,
            ["-ffixed-x29"] = true,
            ["-imacros"] = true,
            ["-mno-embedded-data"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-dsym-dir"] = true,
            ["-mno-msa"] = true,
            ["-emit-merged-ifs"] = true,
            ["-funroll-loops"] = true,
            ["-MG"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-P"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fcoroutines-ts"] = true,
            ["-dM"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-mlong-double-128"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fxray-link-deps"] = true,
            ["-iwithprefix"] = true,
            ["-fconvergent-functions"] = true,
            ["-ffixed-x10"] = true,
            ["-fno-stack-protector"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-mmark-bti-property"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-ftime-trace"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-ffixed-x3"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fno-standalone-debug"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-temp-file"] = true,
            ["-MD"] = true,
            ["-fno-lto"] = true,
            ["-fobjc-weak"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fstack-protector-all"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fcall-saved-x18"] = true,
            ["-MF"] = true,
            ["-ffixed-x5"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-Qn"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-femulated-tls"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-ffixed-x18"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fstandalone-debug"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-gcodeview"] = true,
            ["-mno-outline"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-index-header-map"] = true,
            ["-S"] = true,
            ["-fsigned-char"] = true,
            ["-fopenmp"] = true,
            ["-fmath-errno"] = true,
            ["-fgnu89-inline"] = true,
            ["-iprefix"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-ffixed-x28"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-iwithsysroot"] = true,
            ["--emit-static-lib"] = true,
            ["--analyzer-output"] = true,
            ["-mglobal-merge"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-mhvx"] = true,
            ["-fopenmp-simd"] = true,
            ["-extract-api"] = true,
            ["-ffixed-x24"] = true,
            ["-fobjc-exceptions"] = true,
            ["-static-openmp"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-mno-madd4"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fno-rtti-data"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fapple-kext"] = true,
            ["-fopenmp-extensions"] = true,
            ["-mlocal-sdata"] = true,
            ["-MJ"] = true,
            ["-mno-mt"] = true,
            ["-nobuiltininc"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-isystem-after"] = true,
            ["-ffixed-x2"] = true,
            ["-fno-access-control"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fno-sycl"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fno-short-wchar"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-mno-global-merge"] = true,
            ["-rewrite-objc"] = true,
            ["-mfp32"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-module-file-info"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-gdwarf-2"] = true,
            ["-fms-compatibility"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-ffixed-x22"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-print-ivar-layout"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-mlvi-cfi"] = true,
            ["-mrecord-mcount"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-ffixed-x27"] = true,
            ["-mlong-calls"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fverbose-asm"] = true,
            ["-pedantic"] = true,
            ["-fno-show-source-location"] = true,
            ["-mibt-seal"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-w"] = true,
            ["-fwritable-strings"] = true,
            ["--cuda-host-only"] = true,
            ["-fpascal-strings"] = true,
            ["-fno-split-stack"] = true,
            ["-mlvi-hardening"] = true,
            ["-ffixed-x15"] = true,
            ["-ffixed-r19"] = true,
            ["-v"] = true,
            ["-module-dependency-dir"] = true,
            ["-arch"] = true,
            ["-mno-nvs"] = true,
            ["-gline-tables-only"] = true,
            ["-fapprox-func"] = true,
            ["-ffixed-x25"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fno-unroll-loops"] = true,
            ["-finstrument-functions"] = true,
            ["-freroll-loops"] = true,
            ["-fno-elide-type"] = true,
            ["-U"] = true,
            ["-meabi"] = true,
            ["-fsplit-stack"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-gembed-source"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-mrelax"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fcommon"] = true,
            ["-pipe"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-mrestrict-it"] = true,
            ["-save-stats"] = true,
            ["-MV"] = true,
            ["-femit-all-decls"] = true,
            ["-fno-pch-codegen"] = true,
            ["-finline-hint-functions"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-x"] = true,
            ["-fno-exceptions"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-mno-local-sdata"] = true,
            ["-I-"] = true,
            ["-mmt"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-mnop-mcount"] = true,
            ["-fobjc-arc"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-ffixed-x14"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fsanitize-stats"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-mllvm"] = true,
            ["-Xclang"] = true,
            ["-mwavefrontsize64"] = true,
            ["-gmodules"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["--precompile"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-help"] = true,
            ["-fcxx-exceptions"] = true,
            ["-mno-restrict-it"] = true,
            ["-Xopenmp-target"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-z"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fmemory-profile"] = true,
            ["-emit-module"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-ffixed-a2"] = true,
            ["-ffixed-r9"] = true,
            ["-isysroot"] = true,
            ["-fstack-clash-protection"] = true,
            ["-ibuiltininc"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-ffixed-x11"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-mfp64"] = true,
            ["-CC"] = true,
            ["-fmodules-ts"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-pthread"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fno-digraphs"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-faligned-allocation"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fno-profile-generate"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fno-autolink"] = true,
            ["-fmodules-search-all"] = true,
            ["-msave-restore"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-gdwarf-5"] = true,
            ["-fno-rtti"] = true,
            ["-working-directory"] = true,
            ["-mexecute-only"] = true,
            ["-mtgsplit"] = true,
            ["-fseh-exceptions"] = true,
            ["-include"] = true,
            ["-Xanalyzer"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-ffixed-a0"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-MP"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-print-target-triple"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-mno-packets"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fdigraphs"] = true,
            ["-fsystem-module"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-save-temps"] = true,
            ["-serialize-diagnostics"] = true,
            ["--verify-debug-info"] = true,
            ["-fdata-sections"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fno-jump-tables"] = true,
            ["-Xassembler"] = true,
            ["-fapplication-extension"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["--analyze"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["--gpu-bundle-output"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-print-supported-cpus"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-maix-struct-return"] = true,
            ["-gdwarf64"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-ffixed-d2"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-MMD"] = true,
            ["-mlong-double-64"] = true,
            ["-ffixed-a6"] = true,
            ["-mno-gpopt"] = true,
            ["-fno-finite-loops"] = true,
            ["-fasync-exceptions"] = true,
            ["-ffixed-x19"] = true,
            ["-print-search-dirs"] = true,
            ["-fsanitize-trap"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fpch-debuginfo"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-mno-hvx"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-ffreestanding"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-C"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-ffixed-point"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fno-strict-return"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-mno-save-restore"] = true,
            ["-ffixed-x13"] = true,
            ["-mnvj"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-M"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fgnu-keywords"] = true,
            ["-fpcc-struct-return"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-ivfsoverlay"] = true,
            ["-shared-libsan"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-emit-llvm"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-D"] = true,
            ["-Xlinker"] = true,
            ["-ffixed-d1"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fxray-instrument"] = true,
            ["-mno-cumode"] = true,
            ["-fsized-deallocation"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-nogpulib"] = true,
            ["-fdebug-types-section"] = true,
            ["--no-cuda-version-check"] = true,
            ["-fno-integrated-as"] = true,
            ["-fno-debug-macro"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-ffixed-x17"] = true,
            ["-traditional-cpp"] = true,
            ["-isystem"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fno-discard-value-names"] = true,
            ["-mmsa"] = true,
            ["-ffixed-x6"] = true,
            ["-ffixed-d7"] = true,
            ["-ffixed-a1"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-dI"] = true,
            ["-nostdinc"] = true,
            ["-mno-relax"] = true,
            ["-fblocks"] = true,
            ["-miamcu"] = true,
            ["-fshort-wchar"] = true,
            ["-fms-extensions"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fgnu-runtime"] = true,
            ["-fglobal-isel"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-ffixed-a4"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fnew-infallible"] = true,
            ["-cl-no-stdinc"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-ffixed-d6"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-MM"] = true,
            ["-ffixed-x31"] = true,
            ["-mlong-double-80"] = true,
            ["-ffixed-x23"] = true,
            ["-G"] = true,
            ["-mgpopt"] = true,
            ["-T"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-mno-tgsplit"] = true,
            ["-c"] = true,
            ["-idirafter"] = true,
            ["-ftrigraphs"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-B"] = true,
            ["-nogpuinc"] = true,
            ["-ffixed-x12"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-msvr4-struct-return"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mpacked-stack"] = true,
            ["-MQ"] = true,
            ["-malign-double"] = true,
            ["-ffixed-a5"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-dependency-dot"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fzvector"] = true,
            ["-mcode-object-v3"] = true,
            ["--version"] = true,
            ["-L"] = true,
            ["-fno-use-init-array"] = true,
            ["-MT"] = true,
            ["-ffixed-d4"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-moutline-atomics"] = true,
            ["-mrtd"] = true,
            ["--cuda-device-only"] = true,
            ["-flto"] = true,
            ["-ffast-math"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-ffixed-x21"] = true,
            ["-fvectorize"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fenable-matrix"] = true,
            ["-fstack-protector"] = true,
            ["-fdeclspec"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-mms-bitfields"] = true,
            ["-fgpu-sanitize"] = true,
            ["-print-runtime-dir"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-mno-nvj"] = true,
            ["-ffixed-a3"] = true,
            ["-gdwarf-3"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fno-common"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-gdwarf32"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mnocrc"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-H"] = true,
            ["-rpath"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mno-execute-only"] = true,
            ["-fdebug-macro"] = true,
            ["-foffload-lto"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-ffinite-loops"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-mcmse"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fno-signed-char"] = true,
            ["-fslp-vectorize"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fno-declspec"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-static-libsan"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fcxx-modules"] = true,
            ["-cl-mad-enable"] = true,
            ["-Qy"] = true,
            ["-mextern-sdata"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fno-global-isel"] = true,
            ["-fprotect-parens"] = true,
            ["-fstrict-enums"] = true,
            ["-fstack-size-section"] = true,
            ["-ffixed-x1"] = true,
            ["-mhvx-qfloat"] = true,
            ["-mpackets"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-verify-pch"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fmodules-decluse"] = true,
            ["-ffixed-x9"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-munaligned-access"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-fsycl"] = true,
            ["-dD"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-ftrapv"] = true,
            ["-fno-cxx-modules"] = true,
            ["-Ttext"] = true,
            ["-fstack-usage"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fgpu-rdc"] = true,
            ["-F"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fcf-protection"] = true,
            ["-fmerge-all-constants"] = true,
            ["-ffunction-sections"] = true,
            ["-mno-memops"] = true,
            ["-undef"] = true,
            ["-fintegrated-as"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-g"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-include-pch"] = true,
            ["--help-hidden"] = true,
            ["-gline-directives-only"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-print-resource-dir"] = true,
            ["-fno-memory-profile"] = true,
            ["-ffixed-d0"] = true,
            ["-fno-fixed-point"] = true,
            ["-fno-addrsig"] = true,
            ["-fembed-bitcode"] = true,
            ["-nohipwrapperinc"] = true,
            ["-ffixed-d3"] = true,
            ["-fprofile-generate"] = true,
            ["--hip-link"] = true,
            ["-ffixed-x8"] = true,
            ["-fshort-enums"] = true,
            ["-fno-trigraphs"] = true,
            ["-faddrsig"] = true,
            ["-fno-show-column"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fropi"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-gdwarf-4"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-mseses"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fjump-tables"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fno-offload-lto"] = true,
            ["-mcumode"] = true,
            ["-cl-opt-disable"] = true,
            ["-membedded-data"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-Xpreprocessor"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-finline-functions"] = true,
            ["-msoft-float"] = true,
            ["-gno-embed-source"] = true,
            ["-fintegrated-cc1"] = true,
            ["-ffixed-x20"] = true,
            ["-mnvs"] = true,
            ["-mno-abicalls"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mfentry"] = true,
            ["-fansi-escape-codes"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["--migrate"] = true,
            ["-fpch-codegen"] = true,
            ["--config"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-new-infallible"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fuse-line-directives"] = true,
            ["-fborland-extensions"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-Tdata"] = true,
            ["-mmemops"] = true,
            ["-fno-spell-checking"] = true,
            ["-cxx-isystem"] = true,
            ["-relocatable-pch"] = true,
            ["-mno-long-calls"] = true,
            ["-ffixed-x30"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-mno-crc"] = true,
            ["-ffixed-x7"] = true,
            ["-mqdsp6-compat"] = true,
            ["-ffixed-x26"] = true,
            ["-fcall-saved-x14"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-dependency-file"] = true,
            ["-Wdeprecated"] = true,
            ["-b"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-mno-movt"] = true,
            ["-pg"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-ffixed-d5"] = true,
            ["-fcs-profile-generate"] = true,
            ["-I"] = true,
            ["-freciprocal-math"] = true,
            ["-E"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-time"] = true,
            ["-iquote"] = true,
            ["-fms-hotpatch"] = true,
            ["-emit-ast"] = true,
            ["-mthread-model"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fignore-exceptions"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-moutline"] = true,
            ["-mrelax-all"] = true,
            ["-frwpi"] = true,
            ["-print-effective-triple"] = true,
            ["-fdiscard-value-names"] = true,
            ["-mabicalls"] = true,
            ["-mcrc"] = true,
            ["-freg-struct-return"] = true,
            ["-mno-unaligned-access"] = true,
            ["-Tbss"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fno-plt"] = true,
            ["-Qunused-arguments"] = true,
            ["-mno-implicit-float"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-ffixed-x4"] = true,
            ["-print-targets"] = true,
            ["-mmadd4"] = true,
            ["-fexceptions"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-o"] = true,
            ["-fno-builtin"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--no-insert-timestamp"] = true,
            ["-S"] = true,
            ["--demangle"] = true,
            ["--Bdynamic"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--kill-at"] = true,
            ["--strip-debug"] = true,
            ["--high-entropy-va"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--shared"] = true,
            ["--version"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--disable-tsaware"] = true,
            ["-dy"] = true,
            ["-o"] = true,
            ["--Bstatic"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--export-all-symbols"] = true,
            ["-l"] = true,
            ["--disable-dynamicbase"] = true,
            ["--fatal-warnings"] = true,
            ["-L"] = true,
            ["--verbose"] = true,
            ["-m"] = true,
            ["-static"] = true,
            ["-dn"] = true,
            ["--no-demangle"] = true,
            ["--help"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["-s"] = true,
            ["--dynamicbase"] = true,
            ["--no-gc-sections"] = true,
            ["--whole-archive"] = true,
            ["--strip-all"] = true,
            ["--no-fatal-warnings"] = true,
            ["--appcontainer"] = true,
            ["--no-whole-archive"] = true,
            ["--enable-auto-import"] = true,
            ["--nxcompat"] = true,
            ["--disable-no-seh"] = true,
            ["-v"] = true,
            ["--exclude-all-symbols"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--no-seh"] = true,
            ["--gc-sections"] = true,
            ["--insert-timestamp"] = true,
            ["--allow-multiple-definition"] = true,
            ["--large-address-aware"] = true,
            ["--disable-auto-import"] = true,
            ["--tsaware"] = true,
            ["--disable-nxcompat"] = true,
            ["--no-dynamicbase"] = true
        }
    }
}