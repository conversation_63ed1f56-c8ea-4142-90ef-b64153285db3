{
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            cross = "arm-linux-androideabi-",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            sdkver = "21",
            ndkver = 25,
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-meabi"] = true,
            ["-mlvi-cfi"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-help"] = true,
            ["-fno-fixed-point"] = true,
            ["-freciprocal-math"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-msoft-float"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-mcode-object-v3"] = true,
            ["-save-stats"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-ffixed-r9"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-faddrsig"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-Qy"] = true,
            ["-traditional-cpp"] = true,
            ["-mno-long-calls"] = true,
            ["-MMD"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-print-ivar-layout"] = true,
            ["-fcs-profile-generate"] = true,
            ["-Qn"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-freg-struct-return"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-nostdinc"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-c"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-mnvj"] = true,
            ["-H"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fjump-tables"] = true,
            ["-emit-module"] = true,
            ["-mstack-arg-probe"] = true,
            ["-Xassembler"] = true,
            ["-MM"] = true,
            ["-trigraphs"] = true,
            ["-fuse-line-directives"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-MP"] = true,
            ["-mrestrict-it"] = true,
            ["-fstack-size-section"] = true,
            ["-mfp64"] = true,
            ["-ffast-math"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-mno-relax"] = true,
            ["-fno-unroll-loops"] = true,
            ["-gdwarf"] = true,
            ["-finstrument-functions"] = true,
            ["-mthread-model"] = true,
            ["-fseh-exceptions"] = true,
            ["-mfentry"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fintegrated-cc1"] = true,
            ["-ffixed-x19"] = true,
            ["--cuda-host-only"] = true,
            ["-print-target-triple"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fstack-protector-all"] = true,
            ["-ffixed-x28"] = true,
            ["-ffixed-x17"] = true,
            ["-fshort-wchar"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fkeep-static-consts"] = true,
            ["-mno-unaligned-access"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fno-rtti-data"] = true,
            ["-fno-elide-constructors"] = true,
            ["-ffixed-d6"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fno-trigraphs"] = true,
            ["-relocatable-pch"] = true,
            ["-iquote"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-mno-movt"] = true,
            ["-ffixed-d4"] = true,
            ["-fcall-saved-x9"] = true,
            ["-ffixed-x7"] = true,
            ["-fmodules-search-all"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fno-builtin"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-print-effective-triple"] = true,
            ["-mno-extern-sdata"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-mmsa"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-mno-save-restore"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fno-finite-loops"] = true,
            ["-fno-new-infallible"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-ffixed-x5"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fblocks"] = true,
            ["-cl-mad-enable"] = true,
            ["-gdwarf-5"] = true,
            ["-fenable-matrix"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-ffixed-x22"] = true,
            ["-working-directory"] = true,
            ["-nobuiltininc"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fsized-deallocation"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-ffixed-x6"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fstack-clash-protection"] = true,
            ["-gcodeview"] = true,
            ["-fstack-protector-strong"] = true,
            ["-ffixed-x10"] = true,
            ["-frwpi"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fvectorize"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fno-operator-names"] = true,
            ["-mno-nvj"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fzvector"] = true,
            ["-mno-madd4"] = true,
            ["-fcxx-exceptions"] = true,
            ["--help-hidden"] = true,
            ["-fno-sycl"] = true,
            ["-gno-embed-source"] = true,
            ["-Xclang"] = true,
            ["-fno-signed-char"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mgpopt"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-ffixed-x26"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fobjc-arc"] = true,
            ["-mno-crc"] = true,
            ["-nogpuinc"] = true,
            ["-emit-llvm"] = true,
            ["-fno-digraphs"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-mnocrc"] = true,
            ["-ffixed-x27"] = true,
            ["-fxray-link-deps"] = true,
            ["-mrecord-mcount"] = true,
            ["-ffixed-x29"] = true,
            ["-Tdata"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-ffixed-x14"] = true,
            ["-U"] = true,
            ["-ffixed-a3"] = true,
            ["-gembed-source"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-mglobal-merge"] = true,
            ["-fconvergent-functions"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-dependency-file"] = true,
            ["-fno-show-source-location"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-ffixed-x18"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["--migrate"] = true,
            ["-fstrict-enums"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-print-runtime-dir"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-gdwarf-4"] = true,
            ["-fno-autolink"] = true,
            ["-S"] = true,
            ["-fcoroutines-ts"] = true,
            ["-x"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-temp-file"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fpch-codegen"] = true,
            ["-fgpu-rdc"] = true,
            ["-fapprox-func"] = true,
            ["-print-search-dirs"] = true,
            ["-mno-tgsplit"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-msvr4-struct-return"] = true,
            ["-foffload-lto"] = true,
            ["-T"] = true,
            ["-freroll-loops"] = true,
            ["-fsplit-stack"] = true,
            ["-mlong-calls"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fno-jump-tables"] = true,
            ["-cl-opt-disable"] = true,
            ["-fno-xray-function-index"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fsystem-module"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-mpacked-stack"] = true,
            ["-fdeclspec"] = true,
            ["-gline-tables-only"] = true,
            ["-fmodules-ts"] = true,
            ["-ftrapv"] = true,
            ["-mhvx-qfloat"] = true,
            ["-z"] = true,
            ["-mmark-bti-property"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-ffixed-x23"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fcall-saved-x11"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-mno-abicalls"] = true,
            ["-CC"] = true,
            ["-fms-compatibility"] = true,
            ["-fintegrated-as"] = true,
            ["-mskip-rax-setup"] = true,
            ["-mno-execute-only"] = true,
            ["-gdwarf32"] = true,
            ["-extract-api"] = true,
            ["-mmt"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-mno-embedded-data"] = true,
            ["-ffixed-x13"] = true,
            ["-mno-global-merge"] = true,
            ["-mcrc"] = true,
            ["-fansi-escape-codes"] = true,
            ["-ffixed-x30"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-moutline"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-mno-mt"] = true,
            ["-femit-all-decls"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fno-global-isel"] = true,
            ["-fno-sanitize-stats"] = true,
            ["--precompile"] = true,
            ["-mno-msa"] = true,
            ["-undef"] = true,
            ["-fapplication-extension"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-rpath"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-fexceptions"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fdata-sections"] = true,
            ["-ffixed-d1"] = true,
            ["-mllvm"] = true,
            ["-mstackrealign"] = true,
            ["-mrelax"] = true,
            ["-mmadd4"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-ffinite-loops"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-ffixed-x24"] = true,
            ["-fmath-errno"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fprofile-generate"] = true,
            ["-M"] = true,
            ["-mno-gpopt"] = true,
            ["-fno-exceptions"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-malign-double"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-dD"] = true,
            ["-fstandalone-debug"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-finline-hint-functions"] = true,
            ["-ffixed-a2"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fms-extensions"] = true,
            ["-v"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-ffixed-x9"] = true,
            ["-mhvx"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-index-header-map"] = true,
            ["-fembed-bitcode"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-ffixed-d7"] = true,
            ["--hip-link"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-mno-cumode"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-module-file-info"] = true,
            ["-fobjc-weak"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-stack-protector"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-include"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-pedantic"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-dsym-dir"] = true,
            ["-mlong-double-80"] = true,
            ["-fdiscard-value-names"] = true,
            ["-gdwarf-3"] = true,
            ["-fcf-protection"] = true,
            ["-fno-lto"] = true,
            ["-save-temps"] = true,
            ["-imacros"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-ibuiltininc"] = true,
            ["-ffixed-x1"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fsave-optimization-record"] = true,
            ["-isystem-after"] = true,
            ["-mnvs"] = true,
            ["-ffixed-x4"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-pthread"] = true,
            ["-ffixed-a4"] = true,
            ["-ffixed-a6"] = true,
            ["-maix-struct-return"] = true,
            ["-dI"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fsycl"] = true,
            ["-fsanitize-trap"] = true,
            ["-femulated-tls"] = true,
            ["-b"] = true,
            ["-fpch-debuginfo"] = true,
            ["-Xpreprocessor"] = true,
            ["-ffixed-x3"] = true,
            ["-mlong-double-128"] = true,
            ["-fdebug-types-section"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fverbose-asm"] = true,
            ["-idirafter"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-miamcu"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-ffixed-x11"] = true,
            ["-cxx-isystem"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-spell-checking"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-ffixed-x31"] = true,
            ["-mno-local-sdata"] = true,
            ["-ffixed-d5"] = true,
            ["-fdigraphs"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-emit-ast"] = true,
            ["-module-dependency-dir"] = true,
            ["-ffixed-point"] = true,
            ["-mno-seses"] = true,
            ["-fno-access-control"] = true,
            ["-fsanitize-stats"] = true,
            ["-fminimize-whitespace"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-G"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fstack-usage"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-MV"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-gcodeview-ghash"] = true,
            ["-o"] = true,
            ["-static-openmp"] = true,
            ["-fmodules-decluse"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-MF"] = true,
            ["-fopenmp"] = true,
            ["-ffixed-x20"] = true,
            ["-fapple-kext"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-mlocal-sdata"] = true,
            ["--cuda-device-only"] = true,
            ["-nohipwrapperinc"] = true,
            ["-B"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fno-integrated-as"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fno-short-wchar"] = true,
            ["-fborland-extensions"] = true,
            ["-fpascal-strings"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-mno-memops"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-fno-profile-generate"] = true,
            ["-mno-restrict-it"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-integrated-cc1"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-mqdsp6-compat"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-ffixed-d3"] = true,
            ["-mbackchain"] = true,
            ["-membedded-data"] = true,
            ["-mtgsplit"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-print-resource-dir"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["--analyzer-output"] = true,
            ["-nogpulib"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-mno-implicit-float"] = true,
            ["-mexecute-only"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-munaligned-access"] = true,
            ["-pipe"] = true,
            ["-Wdeprecated"] = true,
            ["-mrelax-all"] = true,
            ["-ftime-trace"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-iprefix"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fno-plt"] = true,
            ["-L"] = true,
            ["-include-pch"] = true,
            ["-P"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fstack-protector"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-Xanalyzer"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-g"] = true,
            ["-iwithprefixbefore"] = true,
            ["-ffreestanding"] = true,
            ["-fslp-vectorize"] = true,
            ["-gmodules"] = true,
            ["-fropi"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-ffixed-a1"] = true,
            ["-gdwarf64"] = true,
            ["-fno-elide-type"] = true,
            ["-fpcc-struct-return"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fmemory-profile"] = true,
            ["-print-targets"] = true,
            ["-fdebug-macro"] = true,
            ["-finline-functions"] = true,
            ["-fnew-infallible"] = true,
            ["-fno-rtti"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-ffixed-x12"] = true,
            ["-mno-neg-immediates"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-ffixed-x25"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fignore-exceptions"] = true,
            ["-fmodules"] = true,
            ["-mno-packets"] = true,
            ["-time"] = true,
            ["-fno-strict-return"] = true,
            ["-gdwarf-2"] = true,
            ["-print-multiarch"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-dependency-dot"] = true,
            ["-static-libsan"] = true,
            ["-ffunction-sections"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["--gpu-bundle-output"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-E"] = true,
            ["-fprotect-parens"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fsigned-char"] = true,
            ["-fno-declspec"] = true,
            ["-ffixed-x8"] = true,
            ["-mfp32"] = true,
            ["-fgnu-runtime"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-flto"] = true,
            ["-funroll-loops"] = true,
            ["-mcmse"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-w"] = true,
            ["-verify-pch"] = true,
            ["-MD"] = true,
            ["-mpackets"] = true,
            ["-mms-bitfields"] = true,
            ["--config"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-iwithprefix"] = true,
            ["-mnop-mcount"] = true,
            ["-fno-split-stack"] = true,
            ["-msave-restore"] = true,
            ["-ffixed-r19"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-mabicalls"] = true,
            ["-F"] = true,
            ["-isystem"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mseses"] = true,
            ["-ffixed-x15"] = true,
            ["-MT"] = true,
            ["-mibt-seal"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-Tbss"] = true,
            ["-Xopenmp-target"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-iwithsysroot"] = true,
            ["-mrtd"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fopenmp-simd"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-cl-finite-math-only"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fshort-enums"] = true,
            ["-mno-hvx"] = true,
            ["-mno-outline"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-MG"] = true,
            ["-Ttext"] = true,
            ["-mno-nvs"] = true,
            ["-isysroot"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-arch"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-C"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-gline-directives-only"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-Xlinker"] = true,
            ["-I-"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-ffixed-a0"] = true,
            ["-fms-hotpatch"] = true,
            ["-mlvi-hardening"] = true,
            ["-Qunused-arguments"] = true,
            ["-fxray-instrument"] = true,
            ["-ffixed-x16"] = true,
            ["-mextern-sdata"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-ffixed-x21"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["--analyze"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["--verify-debug-info"] = true,
            ["-mmemops"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fcxx-modules"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fno-use-init-array"] = true,
            ["-D"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-MJ"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fno-debug-macro"] = true,
            ["-mlong-double-64"] = true,
            ["--no-cuda-version-check"] = true,
            ["-ftrigraphs"] = true,
            ["-ffixed-d0"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-MQ"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fno-common"] = true,
            ["-fno-show-column"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fopenmp-extensions"] = true,
            ["-ffixed-x2"] = true,
            ["-faligned-allocation"] = true,
            ["-pg"] = true,
            ["-ffixed-d2"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-shared-libsan"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-print-supported-cpus"] = true,
            ["--emit-static-lib"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fgpu-sanitize"] = true,
            ["--version"] = true,
            ["-serialize-diagnostics"] = true,
            ["-I"] = true,
            ["-mcumode"] = true,
            ["-fasync-exceptions"] = true,
            ["-fcoverage-mapping"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fno-offload-lto"] = true,
            ["-moutline-atomics"] = true,
            ["-fno-addrsig"] = true,
            ["-fgnu89-inline"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fcommon"] = true,
            ["-fgnu-keywords"] = true,
            ["-dM"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-ffine-grained-bitfield-accesses"] = true
        }
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--no-demangle"] = true,
            ["-L"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--no-whole-archive"] = true,
            ["--disable-tsaware"] = true,
            ["--no-dynamicbase"] = true,
            ["--whole-archive"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--Bdynamic"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--enable-auto-import"] = true,
            ["--kill-at"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--nxcompat"] = true,
            ["-dn"] = true,
            ["--disable-dynamicbase"] = true,
            ["--no-seh"] = true,
            ["-s"] = true,
            ["-dy"] = true,
            ["--dynamicbase"] = true,
            ["--export-all-symbols"] = true,
            ["--Bstatic"] = true,
            ["--no-insert-timestamp"] = true,
            ["--demangle"] = true,
            ["-S"] = true,
            ["--strip-debug"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-fatal-warnings"] = true,
            ["--help"] = true,
            ["-l"] = true,
            ["--disable-no-seh"] = true,
            ["--high-entropy-va"] = true,
            ["--gc-sections"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--insert-timestamp"] = true,
            ["--tsaware"] = true,
            ["--no-gc-sections"] = true,
            ["--strip-all"] = true,
            ["--version"] = true,
            ["--appcontainer"] = true,
            ["-static"] = true,
            ["--verbose"] = true,
            ["--disable-nxcompat"] = true,
            ["--fatal-warnings"] = true,
            ["-m"] = true,
            ["--shared"] = true,
            ["--exclude-all-symbols"] = true,
            ["--large-address-aware"] = true,
            ["-o"] = true,
            ["-v"] = true,
            ["--disable-auto-import"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    }
}