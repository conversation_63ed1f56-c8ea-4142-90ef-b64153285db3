{
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-static"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--nxcompat"] = true,
            ["--dynamicbase"] = true,
            ["--insert-timestamp"] = true,
            ["--high-entropy-va"] = true,
            ["--whole-archive"] = true,
            ["--appcontainer"] = true,
            ["--verbose"] = true,
            ["--fatal-warnings"] = true,
            ["--strip-all"] = true,
            ["--disable-nxcompat"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--demangle"] = true,
            ["--kill-at"] = true,
            ["-L"] = true,
            ["--tsaware"] = true,
            ["-s"] = true,
            ["--shared"] = true,
            ["-dy"] = true,
            ["--gc-sections"] = true,
            ["-l"] = true,
            ["-o"] = true,
            ["--exclude-all-symbols"] = true,
            ["--enable-auto-import"] = true,
            ["--disable-dynamicbase"] = true,
            ["--no-whole-archive"] = true,
            ["--disable-no-seh"] = true,
            ["--export-all-symbols"] = true,
            ["-S"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--no-gc-sections"] = true,
            ["--disable-auto-import"] = true,
            ["--no-seh"] = true,
            ["--version"] = true,
            ["-dn"] = true,
            ["--Bstatic"] = true,
            ["-m"] = true,
            ["--no-fatal-warnings"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--no-demangle"] = true,
            ["--no-dynamicbase"] = true,
            ["--large-address-aware"] = true,
            ["--strip-debug"] = true,
            ["--no-insert-timestamp"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--allow-multiple-definition"] = true,
            ["--help"] = true,
            ["--disable-tsaware"] = true,
            ["--Bdynamic"] = true,
            ["-v"] = true
        }
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            ndkver = 25,
            sdkver = "21",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            cross = "arm-linux-androideabi-",
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-assume-sane-operator-new"] = true,
            ["-mbackchain"] = true,
            ["-fjump-tables"] = true,
            ["-fno-elide-type"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fwritable-strings"] = true,
            ["-freroll-loops"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-mlvi-hardening"] = true,
            ["-mno-packets"] = true,
            ["-mfp32"] = true,
            ["-fno-elide-constructors"] = true,
            ["-print-supported-cpus"] = true,
            ["-mmemops"] = true,
            ["-fno-temp-file"] = true,
            ["-mnvs"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-ffixed-x6"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fno-common"] = true,
            ["-emit-module"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fsigned-char"] = true,
            ["-ffixed-a2"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-faligned-allocation"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-mlong-double-80"] = true,
            ["-fkeep-static-consts"] = true,
            ["-M"] = true,
            ["-ftime-trace"] = true,
            ["-ffixed-x7"] = true,
            ["-ffinite-loops"] = true,
            ["-mno-seses"] = true,
            ["-fno-operator-names"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-mno-abicalls"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-gdwarf-3"] = true,
            ["-ffixed-x23"] = true,
            ["-mlvi-cfi"] = true,
            ["-fxray-instrument"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fprotect-parens"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-L"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-emit-ast"] = true,
            ["-fgnu-runtime"] = true,
            ["-ffixed-a1"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fmodules"] = true,
            ["-msave-restore"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fmerge-all-constants"] = true,
            ["-ffixed-x20"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-fno-show-source-location"] = true,
            ["-ffixed-d4"] = true,
            ["-fzvector"] = true,
            ["-gmodules"] = true,
            ["-gline-tables-only"] = true,
            ["-fmodules-search-all"] = true,
            ["-cl-opt-disable"] = true,
            ["-Xclang"] = true,
            ["-fno-debug-macro"] = true,
            ["-fno-strict-return"] = true,
            ["-fropi"] = true,
            ["-isysroot"] = true,
            ["-mno-mt"] = true,
            ["-MF"] = true,
            ["-ffixed-d6"] = true,
            ["-fsplit-stack"] = true,
            ["-dI"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fno-spell-checking"] = true,
            ["-fopenmp"] = true,
            ["-ffixed-d1"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["--cuda-device-only"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-fapplication-extension"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mmadd4"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fasync-exceptions"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-help"] = true,
            ["-mpackets"] = true,
            ["-iquote"] = true,
            ["-membedded-data"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-mmark-bti-property"] = true,
            ["--help-hidden"] = true,
            ["-frwpi"] = true,
            ["-fobjc-weak"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-P"] = true,
            ["-isystem"] = true,
            ["-mno-implicit-float"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-ffixed-x16"] = true,
            ["-fno-plt"] = true,
            ["-fmemory-profile"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-undef"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-emit-interface-stubs"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fcall-saved-x8"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-dsym-dir"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-mno-gpopt"] = true,
            ["-fslp-vectorize"] = true,
            ["-mno-embedded-data"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fapprox-func"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-mlocal-sdata"] = true,
            ["-fcf-protection"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-ffixed-x9"] = true,
            ["-Qn"] = true,
            ["-save-stats"] = true,
            ["-mcode-object-v3"] = true,
            ["-iwithprefix"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fstrict-enums"] = true,
            ["-fshort-wchar"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-mno-restrict-it"] = true,
            ["-Wdeprecated"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-flto"] = true,
            ["-E"] = true,
            ["-fverbose-asm"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-x3"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-ffixed-a4"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-mno-long-calls"] = true,
            ["-c"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fwasm-exceptions"] = true,
            ["-mabicalls"] = true,
            ["-mhvx-qfloat"] = true,
            ["-D"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fprofile-generate"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fno-xray-function-index"] = true,
            ["-mrelax-all"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-ffixed-a3"] = true,
            ["-fno-stack-protector"] = true,
            ["-femit-all-decls"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fborland-extensions"] = true,
            ["-traditional-cpp"] = true,
            ["-mrtd"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-mstackrealign"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-signed-char"] = true,
            ["-Xanalyzer"] = true,
            ["-miamcu"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fcxx-exceptions"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fno-lto"] = true,
            ["-fpascal-strings"] = true,
            ["-emit-llvm"] = true,
            ["-gcodeview"] = true,
            ["-fcs-profile-generate"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-mno-execute-only"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-maix-struct-return"] = true,
            ["-fsanitize-stats"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-save-temps"] = true,
            ["-ffixed-x13"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["--analyze"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fobjc-arc"] = true,
            ["-gdwarf"] = true,
            ["-fglobal-isel"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fno-fixed-point"] = true,
            ["-ffixed-x14"] = true,
            ["-fmath-errno"] = true,
            ["-fno-standalone-debug"] = true,
            ["-mwavefrontsize64"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fshort-enums"] = true,
            ["-fcall-saved-x14"] = true,
            ["-ffixed-x28"] = true,
            ["-fpch-codegen"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fno-memory-profile"] = true,
            ["--config"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-mnop-mcount"] = true,
            ["-MQ"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-mseses"] = true,
            ["-finline-functions"] = true,
            ["-MP"] = true,
            ["-imacros"] = true,
            ["-mllvm"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-declspec"] = true,
            ["-gno-embed-source"] = true,
            ["-dM"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fsystem-module"] = true,
            ["--precompile"] = true,
            ["-mmt"] = true,
            ["-Tdata"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fconvergent-functions"] = true,
            ["-mrecord-mcount"] = true,
            ["-mqdsp6-compat"] = true,
            ["-ffixed-d2"] = true,
            ["-trigraphs"] = true,
            ["-I-"] = true,
            ["-ffixed-x4"] = true,
            ["-ffixed-x18"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-working-directory"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fcall-saved-x13"] = true,
            ["-munaligned-access"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-mibt-seal"] = true,
            ["-gdwarf-5"] = true,
            ["-msoft-float"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mno-extern-sdata"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-mno-memops"] = true,
            ["-fintegrated-cc1"] = true,
            ["--version"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-ffixed-a5"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-malign-double"] = true,
            ["-fenable-matrix"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-ffixed-x10"] = true,
            ["-ffixed-x25"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-mrelax"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-ffixed-d3"] = true,
            ["-ibuiltininc"] = true,
            ["-mno-local-sdata"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-mhvx"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-cl-no-stdinc"] = true,
            ["-mcumode"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-B"] = true,
            ["-iwithsysroot"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-ffixed-x12"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-MT"] = true,
            ["-ffixed-x31"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-mfp64"] = true,
            ["-fexceptions"] = true,
            ["-dependency-dot"] = true,
            ["-fstack-protector"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fno-global-isel"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-include"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-MM"] = true,
            ["-b"] = true,
            ["-mcrc"] = true,
            ["--emit-static-lib"] = true,
            ["-fignore-exceptions"] = true,
            ["-iprefix"] = true,
            ["-ffixed-x27"] = true,
            ["-mno-relax"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mlong-calls"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fstack-usage"] = true,
            ["--migrate"] = true,
            ["-mno-nvs"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-cxx-isystem"] = true,
            ["-nobuiltininc"] = true,
            ["-ffixed-a6"] = true,
            ["-fmodules-decluse"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-mnvj"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fno-use-init-array"] = true,
            ["-mno-nvj"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fno-show-column"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fstack-size-section"] = true,
            ["--hip-link"] = true,
            ["-ffixed-x5"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-Qunused-arguments"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-print-search-dirs"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-print-targets"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-fno-short-wchar"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-mcmse"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-ffixed-x11"] = true,
            ["-Xopenmp-target"] = true,
            ["-Xlinker"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-ffixed-x24"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-mnocrc"] = true,
            ["-fno-integrated-as"] = true,
            ["-fforce-enable-int128"] = true,
            ["-print-multiarch"] = true,
            ["-gdwarf32"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-Ttext"] = true,
            ["-mno-neg-immediates"] = true,
            ["-faddrsig"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-g"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fapple-kext"] = true,
            ["-dependency-file"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-Xassembler"] = true,
            ["-fdata-sections"] = true,
            ["-fno-new-infallible"] = true,
            ["-nogpulib"] = true,
            ["-fopenmp-simd"] = true,
            ["-ffixed-a0"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fno-discard-value-names"] = true,
            ["-gembed-source"] = true,
            ["-print-target-triple"] = true,
            ["-moutline-atomics"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-msvr4-struct-return"] = true,
            ["-cl-mad-enable"] = true,
            ["-fgnu-keywords"] = true,
            ["-femulated-tls"] = true,
            ["-U"] = true,
            ["-fno-jump-tables"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-gdwarf-4"] = true,
            ["-fintegrated-as"] = true,
            ["-Qy"] = true,
            ["-MD"] = true,
            ["-fembed-bitcode"] = true,
            ["-o"] = true,
            ["-pthread"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-ffixed-x26"] = true,
            ["-ffixed-x19"] = true,
            ["-fseh-exceptions"] = true,
            ["-fstack-protector-all"] = true,
            ["-fsycl"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-ftrapv"] = true,
            ["-arch"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-mno-msa"] = true,
            ["--verify-debug-info"] = true,
            ["-fno-rtti"] = true,
            ["-nogpuinc"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-module-file-info"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-mlong-double-64"] = true,
            ["-fms-compatibility"] = true,
            ["-finstrument-functions"] = true,
            ["-fdebug-types-section"] = true,
            ["-fsanitize-trap"] = true,
            ["-fno-digraphs"] = true,
            ["-Tbss"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-mextern-sdata"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-fno-sycl"] = true,
            ["-mno-tgsplit"] = true,
            ["-fno-profile-generate"] = true,
            ["-MG"] = true,
            ["-fms-hotpatch"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-print-ivar-layout"] = true,
            ["-foffload-lto"] = true,
            ["-nostdinc"] = true,
            ["-S"] = true,
            ["-ffixed-x8"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-ffixed-x2"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-ffixed-x21"] = true,
            ["-v"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-G"] = true,
            ["-fvectorize"] = true,
            ["-mrestrict-it"] = true,
            ["-fdeclspec"] = true,
            ["--gpu-bundle-output"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-moutline"] = true,
            ["-fno-access-control"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-mms-bitfields"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-dD"] = true,
            ["-mno-cumode"] = true,
            ["-mno-global-merge"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-I"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fno-offload-lto"] = true,
            ["-ftrigraphs"] = true,
            ["-T"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-extract-api"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-fno-trigraphs"] = true,
            ["-mpacked-stack"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-ffixed-d5"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-ffixed-r19"] = true,
            ["-ffunction-sections"] = true,
            ["-fno-autolink"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-mmsa"] = true,
            ["-gcodeview-ghash"] = true,
            ["-MJ"] = true,
            ["-mthread-model"] = true,
            ["-x"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fopenmp-extensions"] = true,
            ["-relocatable-pch"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-freciprocal-math"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-MV"] = true,
            ["--no-cuda-version-check"] = true,
            ["-funroll-loops"] = true,
            ["-CC"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fmodules-ts"] = true,
            ["-fno-split-stack"] = true,
            ["-ffast-math"] = true,
            ["-mtgsplit"] = true,
            ["-verify-pch"] = true,
            ["-fdebug-macro"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fpcc-struct-return"] = true,
            ["-freg-struct-return"] = true,
            ["-fsized-deallocation"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-static-openmp"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fxray-link-deps"] = true,
            ["-include-pch"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fpch-debuginfo"] = true,
            ["-w"] = true,
            ["-mno-crc"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-mno-madd4"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-mno-outline"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-fno-builtin"] = true,
            ["-ffixed-x29"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-finline-hint-functions"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-H"] = true,
            ["-fms-extensions"] = true,
            ["-shared-libsan"] = true,
            ["-mglobal-merge"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-time"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fno-rtti-data"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-rpath"] = true,
            ["-fminimize-whitespace"] = true,
            ["-z"] = true,
            ["-idirafter"] = true,
            ["-ffreestanding"] = true,
            ["-gdwarf-2"] = true,
            ["-print-resource-dir"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-ffixed-r9"] = true,
            ["-ffixed-point"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-ffixed-x15"] = true,
            ["--cuda-host-only"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-static-libsan"] = true,
            ["-pipe"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fstandalone-debug"] = true,
            ["-fnew-infallible"] = true,
            ["-fcommon"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-mlong-double-128"] = true,
            ["-fno-addrsig"] = true,
            ["-C"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-mno-movt"] = true,
            ["-isystem-after"] = true,
            ["-ffixed-d7"] = true,
            ["-ffixed-x17"] = true,
            ["-gline-directives-only"] = true,
            ["-index-header-map"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fno-exceptions"] = true,
            ["--analyzer-output"] = true,
            ["-ffixed-x22"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-mexecute-only"] = true,
            ["-mno-save-restore"] = true,
            ["-mgpopt"] = true,
            ["-ffixed-d0"] = true,
            ["-meabi"] = true,
            ["-ivfsoverlay"] = true,
            ["-gdwarf64"] = true,
            ["-print-effective-triple"] = true,
            ["-fno-unique-section-names"] = true,
            ["-F"] = true,
            ["-ffixed-x30"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-module-dependency-dir"] = true,
            ["-fblocks"] = true,
            ["-Xpreprocessor"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-mfentry"] = true,
            ["-fcxx-modules"] = true,
            ["-fno-finite-loops"] = true,
            ["-pedantic"] = true,
            ["-fgnu89-inline"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-print-runtime-dir"] = true,
            ["-fdigraphs"] = true,
            ["-mno-lvi-cfi"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-mno-hvx"] = true,
            ["-pg"] = true,
            ["-fuse-line-directives"] = true,
            ["-MMD"] = true,
            ["-fgpu-rdc"] = true
        }
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}