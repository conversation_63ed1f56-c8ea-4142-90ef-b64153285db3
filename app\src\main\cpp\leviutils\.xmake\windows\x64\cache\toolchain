{
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            plat = "android",
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        toolname = "clangxx"
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        __checked = true,
        plat = "android"
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolchain_info = {
            plat = "android",
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        toolname = "clangxx"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sdkver = "21",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndkver = 25,
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        plat = "android",
        __global = true,
        arch = "armeabi-v7a",
        cross = "arm-linux-androideabi-",
        __checked = true
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        arch = "armeabi-v7a",
        __checked = true,
        plat = "android"
    }
}