{
    ["envs_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        plat = "android",
        __global = true,
        __checked = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a"
        }
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        cross = "arm-linux-androideabi-",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        plat = "android",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        ndk_sdkver = "21",
        ndkver = 25,
        arch = "armeabi-v7a",
        __checked = true,
        __global = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]]
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        plat = "android",
        __global = true,
        __checked = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            name = "ndk",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            arch = "armeabi-v7a"
        }
    }
}