{
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndk_sdkver = "21",
        __global = true,
        ndkver = 25,
        __checked = true,
        plat = "android",
        cross = "arm-linux-androideabi-",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        arch = "armeabi-v7a",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        arch = "armeabi-v7a",
        plat = "android",
        __global = true
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        arch = "armeabi-v7a",
        plat = "android",
        __global = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            name = "ndk",
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    }
}