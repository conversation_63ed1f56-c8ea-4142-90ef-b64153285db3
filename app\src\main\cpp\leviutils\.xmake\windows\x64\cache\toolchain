{
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        arch = "armeabi-v7a",
        __checked = true
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        arch = "armeabi-v7a",
        __checked = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        toolchain_info = {
            plat = "android",
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        toolchain_info = {
            plat = "android",
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        cross = "arm-linux-androideabi-",
        plat = "android",
        ndkver = 25,
        __checked = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        __global = true,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sdkver = "21"
    }
}