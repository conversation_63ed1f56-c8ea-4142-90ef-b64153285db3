{
    envs_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    gfortran_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    clang_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = false
    },
    yasm_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    go_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        arch = "armeabi-v7a",
        __global = true,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        __checked = true,
        cross = "arm-linux-androideabi-",
        plat = "android",
        ndkver = 25,
        ndk_sdkver = "21",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        plat = "android",
        arch = "armeabi-v7a",
        __global = true
    },
    gcc_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = false
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        plat = "android",
        arch = "armeabi-v7a",
        __global = true
    }
}