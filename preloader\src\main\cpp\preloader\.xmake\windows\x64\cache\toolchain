{
    envs_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    clang_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = false
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        arch = "x64",
        __checked = false
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        arch = "armeabi-v7a",
        __checked = true
    },
    gfortran_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    gcc_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = false
    },
    yasm_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        arch = "armeabi-v7a",
        __checked = true
    },
    go_arch_x86_64_plat_msys = {
        plat = "msys",
        arch = "x86_64",
        __checked = true
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            name = "ndk",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        }
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        arch = "armeabi-v7a",
        __checked = true,
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndkver = 25,
        ndk_sdkver = "21",
        cross = "arm-linux-androideabi-",
        plat = "android",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
    }
}