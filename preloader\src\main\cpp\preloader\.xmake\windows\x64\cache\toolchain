{
    gfortran_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    zig_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        __global = true,
        plat = "android",
        __checked = true
    },
    yasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    rust_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    go_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    clang_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    fpc_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    nim_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        toolchain_info = {
            name = "ndk",
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx"
    },
    msvc_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    nasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    swift_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        toolchain_info = {
            name = "ndk",
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        toolname = "clangxx"
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        arch = "armeabi-v7a",
        __global = true,
        plat = "android",
        __checked = true
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndkver = 25,
        ndk_sdkver = "21",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        plat = "android",
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        arch = "armeabi-v7a",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        __global = true,
        cross = "arm-linux-androideabi-",
        __checked = true
    },
    cuda_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    }
}