{
    clang_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        __checked = true,
        __global = true
    },
    rust_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    fpc_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        ndkver = 25,
        __global = true,
        arch = "armeabi-v7a",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        ndk_sdkver = "21",
        __checked = true,
        cross = "arm-linux-androideabi-",
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        plat = "android",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
    },
    yasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    nim_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    nasm_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    swift_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    go_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    cuda_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        arch = "armeabi-v7a",
        __checked = true,
        __global = true
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            arch = "armeabi-v7a",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    gfortran_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = true
    },
    zig_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    },
    msvc_arch_x64_plat_windows = {
        arch = "x64",
        plat = "windows",
        __checked = false
    }
}