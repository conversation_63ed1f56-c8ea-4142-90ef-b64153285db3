{
    go_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    msvc_arch_x64_plat_windows = {
        plat = "windows",
        __checked = false,
        arch = "x64"
    },
    ["tool_target_preloader_android_armeabi-v7a_sh"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk",
            arch = "armeabi-v7a"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    gcc_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = false,
        arch = "x86_64"
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        __checked = true,
        arch = "armeabi-v7a"
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        __checked = true,
        cross = "arm-linux-androideabi-",
        ndk_sdkver = "21",
        plat = "android",
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        arch = "armeabi-v7a",
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        ndkver = 25,
        __global = true,
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
    },
    clang_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = false,
        arch = "x86_64"
    },
    ["tool_target_preloader_android_armeabi-v7a_cxx"] = {
        toolname = "clangxx",
        toolchain_info = {
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android",
            name = "ndk",
            arch = "armeabi-v7a"
        },
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["rust_arch_armeabi-v7a_plat_android"] = {
        plat = "android",
        __global = true,
        __checked = true,
        arch = "armeabi-v7a"
    },
    yasm_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    gfortran_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    },
    envs_arch_x86_64_plat_msys = {
        plat = "msys",
        __checked = true,
        arch = "x86_64"
    }
}