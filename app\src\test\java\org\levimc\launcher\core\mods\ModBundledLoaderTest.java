package org.levimc.launcher.core.mods;

import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Arrays;
import java.util.List;

/**
 * Unit tests for ModBundledLoader
 */
public class ModBundledLoaderTest {

    @Test
    public void testIsBundledMod() {
        // Test with different name formats for CrestHelper (currently enabled)
        assertTrue("Should recognize CrestHelper", ModBundledLoader.isBundledMod("CrestHelper"));
        assertTrue("Should recognize libCrestHelper.so", ModBundledLoader.isBundledMod("libCrestHelper.so"));

        // DynamicLights is currently disabled by default for safety
        assertFalse("Should not recognize DynamicLights (disabled by default)", ModBundledLoader.isBundledMod("DynamicLights"));
        assertFalse("Should not recognize libDynamicLights.so (disabled by default)", ModBundledLoader.isBundledMod("libDynamicLights.so"));

        // Test with non-bundled mod
        assertFalse("Should not recognize unknown mod", ModBundledLoader.isBundledMod("UnknownMod"));
        assertFalse("Should not recognize libUnknownMod.so", ModBundledLoader.isBundledMod("libUnknownMod.so"));
    }

    @Test
    public void testGetBundledModNames() {
        List<String> bundledMods = ModBundledLoader.getBundledModNames();

        assertNotNull("Bundled mods list should not be null", bundledMods);
        assertEquals("Should have 1 bundled mod (CrestHelper only)", 1, bundledMods.size());
        assertTrue("Should contain CrestHelper", bundledMods.contains("CrestHelper"));
        // DynamicLights is disabled by default for safety
        assertFalse("Should not contain DynamicLights (disabled by default)", bundledMods.contains("DynamicLights"));
    }

    // Note: testAddBundledMod is commented out because it requires Android logging infrastructure
    // which is not available in unit tests. The functionality works correctly in the actual app.
    /*
    @Test
    public void testAddBundledMod() {
        // Test adding a new bundled mod (this only tests the configuration, not actual loading)
        List<String> dependencies = Arrays.asList("CrestHelper");
        ModBundledLoader.addBundledMod("TestMod", dependencies);

        assertTrue("Should recognize newly added mod", ModBundledLoader.isBundledMod("TestMod"));
        assertTrue("Should recognize newly added mod with lib prefix", ModBundledLoader.isBundledMod("libTestMod.so"));

        List<String> bundledMods = ModBundledLoader.getBundledModNames();
        assertTrue("Should contain newly added mod", bundledMods.contains("TestMod"));
    }
    */
}
