{
    cmdlines = {
        "xmake build -v",
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\modules\\\\private\\\\utils\\\\statistics.lua" --verbose]],
        [[xmake lua "C:\\\\Program Files\\\\xmake\\\\actions\\\\build\\\\cleaner.lua" --verbose]],
        [[xmake install -v -o D:\LeviLaunchroid\app\build\xmake\src\main\jniLibs\arm64-v8a]],
        [[xmake lua -v D:\LeviLaunchroid\app\build\xmake\install_artifacts.lua -o D:\LeviLaunchroid\app\src\main\jniLibs -a arm64-v8a]],
        [[xmake f -c -y -v -p android -a armeabi-v7a -m release --ndk=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653 --buildir=D:\LeviLaunchroid\app\build\xmake]],
        "xmake build -v",
        [[xmake install -v -o D:\LeviLaunchroid\app\build\xmake\src\main\jniLibs\armeabi-v7a]],
        [[xmake lua -v D:\LeviLaunchroid\app\build\xmake\install_artifacts.lua -o D:\LeviLaunchroid\app\src\main\jniLibs -a armeabi-v7a]]
    }
}