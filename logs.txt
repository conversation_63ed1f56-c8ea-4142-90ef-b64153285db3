2025-07-29 18:27:41.442 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:27:41.450 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@2f3909d
2025-07-29 18:27:41.514 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:27:41.537 24949-25052 LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-29 18:27:41.537 24949-25052 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:27:41.537 24949-25052 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:27:41.538 24949-25052 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: launcher.dex
2025-07-29 18:27:41.539 24949-25052 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: launcher.dex
2025-07-29 18:27:41.549 24949-24949 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-29 18:27:41.588 24949-24949 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@c122871
2025-07-29 18:27:41.589 24949-24949 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@4babbc4
2025-07-29 18:27:41.589 24949-24949 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{64fdaad V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:308 
2025-07-29 18:27:41.592 24949-24949 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-29 18:27:41.593 24949-24990 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-29 18:27:41.602 24949-24949 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'cf18c93', fd=159
2025-07-29 18:27:41.602 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:27:41.603 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-29 18:27:41.603 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@64fdaad IsHRR=false TM=true
2025-07-29 18:27:41.618 24949-24949 BufferQueueConsumer     org.levimc.launcher                  D  [](id:617500000002,api:0,p:-1,c:24949) connect: controlledByApp=false
2025-07-29 18:27:41.619 24949-24949 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:0,s:0) constructor()
2025-07-29 18:27:41.619 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@36f01e2 mNativeObject= 0xb4000078f5dcb800 sc.mNativeObject= 0xb4000078f2e87400 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-29 18:27:41.619 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@36f01e2 mNativeObject= 0xb4000078f5dcb800 sc.mNativeObject= 0xb4000078f2e87400 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-29 18:27:41.619 24949-24949 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-29 18:27:41.619 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=11 res=0x3 s={true 0xb4000078f2f18800} ch=true seqId=0
2025-07-29 18:27:41.620 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-29 18:27:41.620 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000078f2f18800} hwInitialized=true
2025-07-29 18:27:41.621 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-29 18:27:41.621 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@36f01e2#6
2025-07-29 18:27:41.622 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@36f01e2#7
2025-07-29 18:27:41.622 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-29 18:27:41.624 24949-25009 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-29 18:27:41.624 24949-25009 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  mWNT: t=0xb4000079e9897080 mBlastBufferQueue=0xb4000078f5dcb800 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-29 18:27:41.624 24949-25009 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-29 18:27:41.626 24949-24990 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-29 18:27:41.627 24949-24990 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=272002803426597(auto) mPendingTransactions.size=0 graphicBufferId=107155139067931 transform=7
2025-07-29 18:27:41.627 24949-24990 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-29 18:27:41.628 24949-24990 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 24949    Tid : 24990
2025-07-29 18:27:41.628 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-29 18:27:41.629 24949-24990 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-29 18:27:41.641 24949-25052 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-29 18:27:41.649 24949-24949 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-29 18:27:41.649 24949-24949 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-29 18:27:41.684 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000078f2f18800}
2025-07-29 18:27:41.684 24949-24949 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-29 18:27:41.684 24949-24949 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-29 18:27:41.688 24949-25022 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=154
2025-07-29 18:27:41.694 24949-25052 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-29 18:27:41.714 24949-24949 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-29 18:27:41.761 24949-25052 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-29 18:27:41.807 24949-25052 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-29 18:27:41.809 24949-25052 LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-29 18:27:41.809 24949-25052 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:27:41.809 24949-25052 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:27:41.809 24949-25052 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:27:41.809 24949-25052 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:27:41.814 24949-25055 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!classes7.dex): ok
2025-07-29 18:27:41.823 24949-25055 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!classes7.dex): ok
2025-07-29 18:27:41.928 24949-25055 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!classes7.dex): ok
2025-07-29 18:27:41.928 24949-25055 Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x7a177635f0
2025-07-29 18:27:41.928 24949-25055 Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-29 18:27:41.929 24949-25055 LeviLogger              org.levimc.launcher                  I  [LeviMC] Starting to load bundled mods...
2025-07-29 18:27:41.931 24949-25055 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-29 18:27:41.931 24949-25055 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!/lib/arm64-v8a/libCrestHelper.so
2025-07-29 18:27:41.931 24949-25055 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-29 18:27:41.931 24949-25055 nativeloader            org.levimc.launcher                  D  Load /data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!/lib/arm64-v8a/libCrestHelper.so using class loader ns clns-7 (caller=/data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!classes9.dex): ok
2025-07-29 18:27:41.931 24949-25055 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully loaded bundled mod: libCrestHelper.so
2025-07-29 18:27:41.933 24949-25055 GlossHook               org.levimc.launcher                  W  GlossHook already init.
2025-07-29 18:27:41.933 24949-25055 GlossHook               org.levimc.launcher                  I  Init linker...
2025-07-29 18:27:41.933 24949-25055 GlossHook               org.levimc.launcher                  I  Android version: 15.0
2025-07-29 18:27:41.941 24949-25055 GlossHook               org.levimc.launcher                  I  Start hook linker...
2025-07-29 18:27:41.950 24949-25055 GlossHook               org.levimc.launcher                  I  Init linker success.
2025-07-29 18:27:44.516 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=no preference, reason=boost timeout, vri=VRI[MainActivity]@2f3909d
2025-07-29 18:27:44.716 24949-25055 GlossHook               org.levimc.launcher                  W  GlossHook already init.
2025-07-29 18:27:44.716 24949-25055 GlossHook               org.levimc.launcher                  W  GlossHook already init.
2025-07-29 18:27:44.721 24949-25055 nativeloader            org.levimc.launcher                  D  Load /data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!/lib/arm64-v8a/libDynamicLights.so using class loader ns clns-7 (caller=/data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!classes9.dex): ok
2025-07-29 18:27:44.721 24949-25055 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully loaded bundled mod: libDynamicLights.so
2025-07-29 18:27:44.721 24949-25055 LeviLogger              org.levimc.launcher                  I  [LeviMC] Finished loading bundled mods. Loaded 2 libraries.
2025-07-29 18:27:44.785 24949-24949 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-29 18:27:44.800 24949-24949 nativeloader            org.levimc.launcher                  D  Load libmaesdk.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libmaesdk.so" not found
2025-07-29 18:27:44.800 24949-24949 MCPE                    org.levimc.launcher                  D  maesdk library not found. This is expected if we're not in Edu mode
2025-07-29 18:27:44.802 24949-24949 nativeloader            org.levimc.launcher                  D  Load libovrfmod.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libovrfmod.so" not found
2025-07-29 18:27:44.803 24949-24949 MCPE                    org.levimc.launcher                  D  OVRfmod library not found
2025-07-29 18:27:44.804 24949-24949 nativeloader            org.levimc.launcher                  D  Load libovrplatformloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libovrplatformloader.so" not found
2025-07-29 18:27:44.805 24949-24949 MCPE                    org.levimc.launcher                  D  OVRplatform library not found
2025-07-29 18:27:44.808 24949-24949 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-29 18:27:44.808 24949-24949 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!/lib/arm64-v8a/libpreloader.so
2025-07-29 18:27:44.808 24949-24949 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-29 18:27:44.808 24949-24949 nativeloader            org.levimc.launcher                  D  Load /data/app/~~HfLhGxxOcWb5lvyO7zfE5Q==/org.levimc.launcher-s3dxec214ZsiihtzKeT8EQ==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/launcher.dex): ok
2025-07-29 18:27:44.828 24949-24949 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/content/res/AssetManager;->addAssetPath(Ljava/lang/String;)I (runtime_flags=0, domain=platform, api=unsupported) from Lcom/mojang/minecraftpe/Launcher; (domain=app) using reflection: allowed
2025-07-29 18:27:44.850 24949-24949 MinecraftPlatform       org.levimc.launcher                  I  MainActivity::onCreate
2025-07-29 18:27:44.857 24949-24949 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@5cc12c1
2025-07-29 18:27:44.861 24949-24949 gti.InputConnection     org.levimc.launcher                  D  InputConnection created
2025-07-29 18:27:44.865 24949-24949 GameActivity            org.levimc.launcher                  I  Looking for library libpreloader.so
2025-07-29 18:27:44.866 24949-24949 GameActivity            org.levimc.launcher                  I  Found library libpreloader.so. Loading...
2025-07-29 18:27:44.869 24949-24949 GameActivity            org.levimc.launcher                  D  GameActivity_register
2025-07-29 18:27:44.870 24949-24949 GameActivity            org.levimc.launcher                  D  SDK version: 35
2025-07-29 18:27:44.872 24949-25059 Minecraft               org.levimc.launcher                  I  android_main starting. internalDataPath is '/data/user/0/org.levimc.launcher/files', externalDataPath is '/storage/emulated/0/Android/data/org.levimc.launcher/files'
2025-07-29 18:27:44.877 24949-25059 MCPE                    org.levimc.launcher                  E  *** setCachedDeviceId(f50350e2feeb45cba6f93a01be8229a9)
2025-07-29 18:27:44.883 24949-25059 Bedrock                 org.levimc.launcher                  I  Breakpad config: directory is: /data/user/0/org.levimc.launcher/crash, sessionid is: 77e8b07f-945d-41a8-ae58-579e9a5b8bcd
2025-07-29 18:27:44.890 24949-25059 libc                    org.levimc.launcher                  W  Access denied finding property "ro.mediatek.platform"
2025-07-29 18:27:44.899 24949-25059 System.out              org.levimc.launcher                  I  getwidth: 2340
2025-07-29 18:27:44.899 24949-25059 System.out              org.levimc.launcher                  I  getheight: 1080
2025-07-29 18:27:44.912 24949-25059 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using AppData dir - CurrentFileStoragePath is now '/data/user/0/org.levimc.launcher'
2025-07-29 18:27:44.983 24949-24949 AppExitInfoHelper       org.levimc.launcher                  I  Registering session ID for ApplicationExitInfo: 77e8b07f-945d-41a8-ae58-579e9a5b8bcd
2025-07-29 18:27:45.001 24949-25059 SwappyDisplayManager    org.levimc.launcher                  I  Using internal com/google/androidgamesdk/SwappyDisplayManager class from dex bytes.
2025-07-29 18:27:45.004 24949-25059 SwappyDisplayManager    org.levimc.launcher                  E  dalvik.system.InMemoryDexClassLoader[DexPathList[[dex file "InMemoryDexFile[cookie=[0, -5476376624987031616]]"],nativeLibraryDirectories=[/system/lib64, /system_ext/lib64]]] couldn't find "libpreloader.so"
2025-07-29 18:27:45.007 24949-25064 SwappyDisplayManager    org.levimc.launcher                  I  Starting looper thread
2025-07-29 18:27:45.009 24949-25059 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb400007983747c40
2025-07-29 18:27:45.009 24949-25059 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-29 18:27:45.022 24949-24949 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze.configure() called with configuration: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-29 18:27:45.023 24949-24949 MinecraftPlatform       org.levimc.launcher                  I  MainActivity::requestPushPermission
2025-07-29 18:27:45.032 24949-24949 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:31)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:27:45.041 24949-24949 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:32)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:27:45.047 24949-24949 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:33)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:27:45.047 24949-25059 AppExitInfoHelper       org.levimc.launcher                  I  Received session ID from ApplicationExitInfo: 8c744324-5c2c-4a94-8e2a-5901e33251fa
2025-07-29 18:27:45.052 24949-24949 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:34)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:27:45.058 24949-24949 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:37)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:27:45.093 24949-24949 InputMethodManager      org.levimc.launcher                  I  invalidateInput
2025-07-29 18:27:45.094 24949-24949 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 303326708; UID 10637; state: ENABLED
2025-07-29 18:27:45.100 24949-24949 MinecraftPE             org.levimc.launcher                  D  onStart
2025-07-29 18:27:45.115 24949-25059 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was gained
2025-07-29 18:27:45.125 24949-24949 Minecraft               org.levimc.launcher                  W  INPUT device id -1- is Crete Controller: false
2025-07-29 18:27:45.125 24949-24949 Minecraft               org.levimc.launcher                  W  INPUT device id 2- is Crete Controller: false
2025-07-29 18:27:45.125 24949-24949 Minecraft               org.levimc.launcher                  W  INPUT device id 3- is Crete Controller: false
2025-07-29 18:27:45.125 24949-24949 Minecraft               org.levimc.launcher                  W  INPUT device id 4- is Crete Controller: false
2025-07-29 18:27:45.125 24949-24949 Minecraft               org.levimc.launcher                  W  INPUT device id 5- is Crete Controller: false
2025-07-29 18:27:45.125 24949-24949 Minecraft               org.levimc.launcher                  W  INPUT device id 6- is Crete Controller: false
2025-07-29 18:27:45.127 24949-24949 Minecraft               org.levimc.launcher                  W  No Xbox Controller Found
2025-07-29 18:27:45.127 24949-24949 Minecraft               org.levimc.launcher                  W  No Playstation Controller Found
2025-07-29 18:27:45.128 24949-24949 Minecraft               org.levimc.launcher                  W  No PS Dualsense Controller Found
2025-07-29 18:27:45.129 24949-24949 MinecraftPE             org.levimc.launcher                  D  onResume
2025-07-29 18:27:45.145 24949-24949 Braze v24....ageManager org.levimc.launcher                  V  Registering InAppMessageManager with activity: com.mojang.minecraftpe.Launcher
2025-07-29 18:27:45.149 24949-24949 Braze v24....ageManager org.levimc.launcher                  D  Subscribing in-app message event subscriber
2025-07-29 18:27:45.149 24949-24949 Braze v24.3.0 .Braze    org.levimc.launcher                  V  The instance is null. Allowing instance initialization
2025-07-29 18:27:45.149 24949-24949 Braze v24.3.0 .Braze    org.levimc.launcher                  V  The instance is null. Allowing instance initialization
2025-07-29 18:27:45.149 24949-24949 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze SDK Initializing
2025-07-29 18:27:45.181 24949-25077 Braze v24....mageLoader org.levimc.launcher                  D  Initializing disk cache
2025-07-29 18:27:45.182 24949-24949 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze SDK loaded in 32 ms.
2025-07-29 18:27:45.182 24949-24949 Braze v24....ageManager org.levimc.launcher                  V  Subscribing sdk data wipe subscriber
2025-07-29 18:27:45.186 24949-25081 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Applying any pending runtime configuration values
2025-07-29 18:27:45.186 24949-25081 Braze v24.3.0 .Braze    org.levimc.launcher                  V  Setting pending config object: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-29 18:27:45.187 24949-25081 Braze v24....onProvider org.levimc.launcher                  I  Setting Braze Override configuration with config: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-29 18:27:45.190 24949-25081 Braze v24....onProvider org.levimc.launcher                  I  Found an override api key. Using it to configure the Braze SDK
2025-07-29 18:27:45.191 24949-25077 Braze v24....mageLoader org.levimc.launcher                  D  Disk cache initialized
2025-07-29 18:27:45.193 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml INTEGER configuration value with primary key 'com_braze_logger_initial_log_level'. Using default value '4'.
2025-07-29 18:27:45.193 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_logger_initial_log_level' and value: '4'
2025-07-29 18:27:45.212 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_custom_endpoint'. Using default value 'null'.
2025-07-29 18:27:45.212 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_custom_endpoint' and value: 'null'
2025-07-29 18:27:45.212 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_firebase_cloud_messaging_registration_enabled' and value: 'false'
2025-07-29 18:27:45.212 24949-25081 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Automatic Firebase Cloud Messaging registration not enabled in configuration. Braze will not register for Firebase Cloud Messaging.
2025-07-29 18:27:45.212 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_push_adm_messaging_registration_enabled' and value: 'false'
2025-07-29 18:27:45.213 24949-25081 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Automatic ADM registration not enabled in configuration. Braze will not register for ADM.
2025-07-29 18:27:45.217 24949-25081 Braze v24.3.0 .Braze    org.levimc.launcher                  V  Starting up a new user dependency manager
2025-07-29 18:27:45.225 24949-24949 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-29 18:27:45.231 24949-24990 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-29 18:27:45.232 24949-24949 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars captionBar, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-29 18:27:45.241 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_session_timeout' and value: '10'
2025-07-29 18:27:45.242 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_session_start_based_timeout_enabled'. Using default value 'false'.
2025-07-29 18:27:45.242 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_session_start_based_timeout_enabled' and value: 'false'
2025-07-29 18:27:45.249 24949-24949 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'b194a89', fd=257
2025-07-29 18:27:45.249 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:27:45.250 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-29 18:27:45.250 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@cd87ee0 IsHRR=false TM=true
2025-07-29 18:27:45.251 24949-24949 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-29 18:27:45.251 24949-24949 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-29 18:27:45.252 24949-24949 SurfaceView@6502654     org.levimc.launcher                  I  onWindowVisibilityChanged(0) true com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{6502654 V.E...... ......I. 0,0-0,0} of VRI[Launcher]@790a5e5
2025-07-29 18:27:45.252 24949-24949 SurfaceView             org.levimc.launcher                  D  105915988 updateSurface: has no frame
2025-07-29 18:27:45.255 24949-24949 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:27:45.255 24949-24949 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:27:45.302 24949-24949 BufferQueueConsumer     org.levimc.launcher                  D  [](id:617500000003,api:0,p:-1,c:24949) connect: controlledByApp=false
2025-07-29 18:27:45.302 24949-24949 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@790a5e5#3](f:0,a:0,s:0) constructor()
2025-07-29 18:27:45.303 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[Launcher]@790a5e5 mNativeObject= 0xb4000078f2eebc00 sc.mNativeObject= 0xb4000078e1e21380 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-29 18:27:45.303 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_trigger_action_minimum_time_interval_seconds' and value: '5'
2025-07-29 18:27:45.303 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@790a5e5 mNativeObject= 0xb4000078f2eebc00 sc.mNativeObject= 0xb4000078e1e21380 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-29 18:27:45.303 24949-24949 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@790a5e5#3](f:0,a:0,s:0) update width=2340 height=1080 format=-3 mTransformHint=4
2025-07-29 18:27:45.304 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=44 res=0x3 s={true 0xb4000078e230f800} ch=true seqId=0
2025-07-29 18:27:45.305 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-29 18:27:45.307 24949-24949 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:27:45.307 24949-24949 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:27:45.308 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000078e230f800} hwInitialized=true
2025-07-29 18:27:45.309 24949-24949 SurfaceView             org.levimc.launcher                  D  105915988 updateSurface: has no frame
2025-07-29 18:27:45.312 24949-24949 SurfaceView@6502654     org.levimc.launcher                  I  windowStopped(false) true com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{6502654 V.E...... ......ID 0,0-2340,1080} of VRI[Launcher]@790a5e5
2025-07-29 18:27:45.312 24949-24949 SurfaceView             org.levimc.launcher                  D  105915988 updateSurface: has no frame
2025-07-29 18:27:45.313 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-29 18:27:45.313 24949-24949 SurfaceView             org.levimc.launcher                  I  105915988 Changes: creating=true format=true size=true visible=true alpha=false hint=true visible=true left=true top=true z=false attached=true lifecycleStrategy=false
2025-07-29 18:27:45.313 24949-25081 Braze v24.....bo.app.d6 org.levimc.launcher                  V  Subscribing to trigger dispatch events.
2025-07-29 18:27:45.318 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_custom_location_providers_list'. Using default value '[]'.
2025-07-29 18:27:45.318 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_custom_location_providers_list' and value: '[]'
2025-07-29 18:27:45.321 24949-24949 BufferQueueConsumer     org.levimc.launcher                  D  [](id:617500000004,api:0,p:-1,c:24949) connect: controlledByApp=false
2025-07-29 18:27:45.322 24949-25081 Braze v24....nceManager org.levimc.launcher                  D  Did not find stored geofences.
2025-07-29 18:27:45.328 24949-24949 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) constructor()
2025-07-29 18:27:45.328 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = null mNativeObject= 0xb4000078f2eee000 sc.mNativeObject= 0xb4000078e1e21f80 format= 4 caller= android.view.SurfaceView.createBlastSurfaceControls:1642 android.view.SurfaceView.updateSurface:1318 android.view.SurfaceView.lambda$new$0:268 android.view.SurfaceView.$r8$lambda$NfZyM_TG8F8lqzaOVZ7noREFjzU:0 android.view.SurfaceView$$ExternalSyntheticLambda1.onPreDraw:0 android.view.ViewTreeObserver.dispatchOnPreDraw:1226 
2025-07-29 18:27:45.328 24949-24949 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) update width=2340 height=1080 format=4 mTransformHint=4
2025-07-29 18:27:45.328 24949-24949 SurfaceView             org.levimc.launcher                  I  105915988 Cur surface: Surface(name=null mNativeObject=0)/@0xc883ed1
2025-07-29 18:27:45.328 24949-24949 SurfaceView@6502654     org.levimc.launcher                  I  pST: sr = Rect(0, 0 - 2340, 1080) sw = 2340 sh = 1080
2025-07-29 18:27:45.329 24949-24949 SurfaceView             org.levimc.launcher                  D  105915988 performSurfaceTransaction RenderWorker position = [0, 0, 2340, 1080] surfaceSize = 2340x1080
2025-07-29 18:27:45.329 24949-24949 SurfaceView@6502654     org.levimc.launcher                  I  updateSurface: mVisible = true mSurface.isValid() = true
2025-07-29 18:27:45.329 24949-24949 SurfaceView@6502654     org.levimc.launcher                  I  updateSurface: mSurfaceCreated = false surfaceChanged = true visibleChanged = true
2025-07-29 18:27:45.333 24949-25081 Braze v24....nceManager org.levimc.launcher                  I  Geofences implicitly disabled via server configuration.
2025-07-29 18:27:45.334 24949-25081 Braze v24....nceManager org.levimc.launcher                  I  ***Geofence API not found. Please include the android-sdk-location module***
2025-07-29 18:27:45.334 24949-25081 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Geofences not set up.
2025-07-29 18:27:45.336 24949-25081 Braze v24.3.0 .o        org.levimc.launcher                  I  ***Location API not found. Please include android-sdk-location module***
2025-07-29 18:27:45.340 24949-24949 SurfaceView             org.levimc.launcher                  I  105915988 visibleChanged -- surfaceCreated
2025-07-29 18:27:45.340 24949-24949 SurfaceView@6502654     org.levimc.launcher                  I  surfaceCreated 1 #8 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{6502654 V.E...... ......ID 0,0-2340,1080}
2025-07-29 18:27:45.342 24949-25081 Braze v24.3.0 .f1       org.levimc.launcher                  D  Did not find stored feature flags.
2025-07-29 18:27:45.367 24949-25059 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was gained
2025-07-29 18:27:45.367 24949-25059 Minecraft               org.levimc.launcher                  W  MinecraftGame::init && MinecraftGame::setSize!
2025-07-29 18:27:45.368 24949-24949 SurfaceView             org.levimc.launcher                  I  105915988 surfaceChanged -- format=4 w=2340 h=1080
2025-07-29 18:27:45.368 24949-24949 SurfaceView@6502654     org.levimc.launcher                  I  surfaceChanged (2340,1080) 1 #8 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{6502654 V.E...... ......ID 0,0-2340,1080}
2025-07-29 18:27:45.369 24949-24949 SurfaceView             org.levimc.launcher                  I  105915988 surfaceRedrawNeeded
2025-07-29 18:27:45.370 24949-24949 SurfaceView             org.levimc.launcher                  I  105915988 finishedDrawing
2025-07-29 18:27:45.370 24949-24949 SurfaceView             org.levimc.launcher                  V  Layout: x=0 y=0 w=2340 h=1080, frame=Rect(0, 0 - 2340, 1080)
2025-07-29 18:27:45.370 24949-25081 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: NONE
2025-07-29 18:27:45.371 24949-25081 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:27:45.371 24949-25081 Braze v24.3.0 .i0       org.levimc.launcher                  D  Data sync started
2025-07-29 18:27:45.371 24949-25081 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.registerDefaultNetworkCallbackForUid(ConnectivityManager.java:5461)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5428)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5402)] [bo.app.i0.d(SourceFile:3)] [bo.app.i0.e(SourceFile:6)] [bo.app.i0.a(SourceFile:10)] [bo.app.n6.<init>(SourceFile:227)] [com.braze.Braze$d.a(SourceFile:63)] [com.braze.Braze$d.invoke(SourceFile:1)] [com.braze.Braze$r2$a.invokeSuspend(SourceFile:2)] [kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)] [kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)] [kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:284)] [kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:85)] [kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:59)] [kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source:1)] [kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:38)] [kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source:1)] [com.braze.Braze$r2.invokeSuspend(SourceFile:2)] [kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)] [kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)] [java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)] [java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)] [java.lang.Thread.run(Thread.java:1119)]
2025-07-29 18:27:45.380 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@790a5e5#9
2025-07-29 18:27:45.380 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@790a5e5#10
2025-07-29 18:27:45.380 24949-25081 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-29 18:27:45.380 24949-25081 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:27:45.382 24949-25076 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-29 18:27:45.382 24949-25076 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:27:45.384 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-29 18:27:45.386 24949-24990 SurfaceView             org.levimc.launcher                  D  105915988 updateSurfacePosition RenderWorker, frameNr = 1, position = [0, 0, 2340, 1080] surfaceSize = 2340x1080
2025-07-29 18:27:45.387 24949-24990 SurfaceView@6502654     org.levimc.launcher                  I  uSP: rtp = Rect(0, 0 - 2340, 1080) rtsw = 2340 rtsh = 1080
2025-07-29 18:27:45.387 24949-24990 SurfaceView@6502654     org.levimc.launcher                  I  onSSPAndSRT: pl = 0 pt = 0 sx = 1.0 sy = 1.0
2025-07-29 18:27:45.387 24949-24990 SurfaceView@6502654     org.levimc.launcher                  I  aOrMT: VRI[Launcher]@790a5e5 t = android.view.SurfaceControl$Transaction@654bfe6 fN = 1 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1792 android.graphics.RenderNode$CompositePositionUpdateListener.positionChanged:398 
2025-07-29 18:27:45.388 24949-24990 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  mWNT: t=0xb4000078e1d80580 mBlastBufferQueue=0xb4000078f2eebc00 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SurfaceView.applyOrMergeTransaction:1723 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1792 
2025-07-29 18:27:45.389 24949-25072 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb4000079833d9600
2025-07-29 18:27:45.390 24949-25010 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-29 18:27:45.390 24949-25072 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-29 18:27:45.390 24949-25010 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  mWNT: t=0xb4000078e1d80b80 mBlastBufferQueue=0xb4000078f2eebc00 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-29 18:27:45.390 24949-25010 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-29 18:27:45.390 24949-25081 Braze v24.3.0 .c1       org.levimc.launcher                  D  Started offline event recovery task.
2025-07-29 18:27:45.396 24949-24990 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@790a5e5#3](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-29 18:27:45.397 24949-24990 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@790a5e5#3](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=272006573278212(auto) mPendingTransactions.size=0 graphicBufferId=107155139067940 transform=7
2025-07-29 18:27:45.398 24949-24990 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-29 18:27:45.399 24949-24990 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 24949    Tid : 24990
2025-07-29 18:27:45.399 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-29 18:27:45.411 24949-24990 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-29 18:27:45.413 24949-24949 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.performTraversals:4497 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:27:45.415 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@790a5e5 mNativeObject= 0xb4000078f2eebc00 sc.mNativeObject= 0xb4000078e1e21380 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:27:45.415 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=0 res=0x0 s={true 0xb4000078e230f800} ch=false seqId=0
2025-07-29 18:27:45.416 24949-24949 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:27:45.417 24949-24949 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:27:45.418 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  updateBoundsLayer: t=android.view.SurfaceControl$Transaction@2b75e22 sc=Surface(name=Bounds for - org.levimc.launcher/com.mojang.minecraftpe.Launcher@0)/@0x70da0b3 frame=2
2025-07-29 18:27:45.420 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-29 18:27:45.422 24949-25009 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  mWNT: t=0xb4000078e1d80d00 mBlastBufferQueue=0xb4000078f2eebc00 fn= 2 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-29 18:27:45.431 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:27:45.432 24949-24949 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-29 18:27:45.433 24949-24949 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=statusBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-29 18:27:45.437 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:27:45.438 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:27:45.438 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-29 18:27:45.440 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-29 18:27:45.440 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-29 18:27:45.440 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-29 18:27:45.440 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-29 18:27:45.440 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-29 18:27:45.444 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:27:45.444 24949-24949 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:27:45.445 24949-24949 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:27:45.446 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@790a5e5#11
2025-07-29 18:27:45.446 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@790a5e5#12
2025-07-29 18:27:45.446 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-29 18:27:45.447 24949-25010 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=3.
2025-07-29 18:27:45.447 24949-25010 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-29 18:27:45.451 24949-24990 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-29 18:27:45.451 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-29 18:27:45.461 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Relayout returned: old=(982,335,1358,745) new=(982,335,1358,745) relayoutAsync=false req=(376,410)8 dur=7 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-29 18:27:45.461 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-29 18:27:45.463 24949-24968 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:3,s:0) destructor()
2025-07-29 18:27:45.463 24949-24968 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@36f01e2#2(BLAST Consumer)2](id:617500000002,api:0,p:-1,c:24949) disconnect
2025-07-29 18:27:45.469 24949-24949 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:3,s:0) destructor()
2025-07-29 18:27:45.470 24949-24949 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@2f3909d#1(BLAST Consumer)1](id:617500000001,api:0,p:-1,c:24949) disconnect
2025-07-29 18:27:45.470 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)8 dur=5 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-29 18:27:45.470 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-29 18:27:45.472 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000078e230f800}
2025-07-29 18:27:45.473 24949-24949 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-29 18:27:45.473 24949-24949 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-29 18:27:45.480 24949-25022 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=145
2025-07-29 18:27:45.489 24949-24949 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.xbox.httpclient.NetworkObserver.Initialize(NetworkObserver.java:72)] [com.mojang.minecraftpe.MainActivity.nativeRunNativeCallbackOnUiThread(Native Method)] [com.mojang.minecraftpe.MainActivity$19.call(MainActivity.java:2136)] [com.mojang.minecraftpe.MainActivity$19.call(MainActivity.java:2133)] [java.util.concurrent.FutureTask.run(FutureTask.java:317)]
2025-07-29 18:27:45.493 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  stopped(true) old = false
2025-07-29 18:27:45.493 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-29 18:27:45.493 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  stopped(true) old = false
2025-07-29 18:27:45.493 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-29 18:27:45.496 24949-25081 Braze v24.3.0 .q        org.levimc.launcher                  D  Messaging session stopped. Adding new messaging session timestamp: 1753792065
2025-07-29 18:27:45.497 24949-25081 Braze v24.3.0 .p        org.levimc.launcher                  V  Closed session with activity: ui.activities.MainActivity
2025-07-29 18:27:45.497 24949-24949 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@4d31d0c
2025-07-29 18:27:45.497 24949-25081 Braze v24.3.0 .u        org.levimc.launcher                  D  Getting the stored open session
2025-07-29 18:27:45.498 24949-24949 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-29 18:27:45.505 24949-24949 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'a890631', fd=211
2025-07-29 18:27:45.506 24949-25081 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 0114c9b6-4caf-4f29-85ba-91eb83a852af
2025-07-29 18:27:45.507 24949-25081 Braze v24.3.0 .u        org.levimc.launcher                  I  Session [0114c9b6-4caf-4f29-85ba-91eb83a852af] being sealed because its end time is over the grace period. Session: 
                                                                                                    MutableSession(sessionId=0114c9b6-4caf-4f29-85ba-91eb83a852af, startTime=1.753791970115E9, endTime=1.753791970132E9, isSealed=false, duration=0)
2025-07-29 18:27:45.510 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.f5 fired: SessionSealedEvent(sealedSession=
                                                                                                    MutableSession(sessionId=0114c9b6-4caf-4f29-85ba-91eb83a852af, startTime=1.753791970115E9, endTime=1.753792065507E9, isSealed=true, duration=95))
2025-07-29 18:27:45.510 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.f5 on 1 subscribers.
2025-07-29 18:27:45.512 24949-24949 WindowManager           org.levimc.launcher                  E  android.view.WindowLeaked: Activity org.levimc.launcher.ui.activities.MainActivity has leaked window com.android.internal.policy.DecorView{64fdaad V.ED..... R.....ID 0,0-376,410 aid=1073741828}[MainActivity] that was originally added here
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1527)
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1502)
                                                                                                    	at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:544)
                                                                                                    	at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
                                                                                                    	at android.app.Dialog.show(Dialog.java:511)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading(MinecraftLauncher.java:308)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher$$ExternalSyntheticLambda4.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-29 18:27:45.512 24949-24949 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#removeView, ty=2, view=com.android.internal.policy.DecorView{64fdaad V.ED..... R.....ID 0,0-376,410 aid=1073741828}[MainActivity], caller=android.view.WindowManagerGlobal.closeAllExceptView:672 android.view.WindowManagerGlobal.closeAll:644 android.app.ActivityThread.handleDestroyActivity:6747 
2025-07-29 18:27:45.516 24949-24949 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-29 18:27:45.518 24949-24949 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@53c74a9
2025-07-29 18:27:45.519 24949-25081 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding session id to event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 95
                                                                                                      },
                                                                                                      "time": 1.753792065515E9,
                                                                                                      "session_id": "0114c9b6-4caf-4f29-85ba-91eb83a852af"
                                                                                                    }
2025-07-29 18:27:45.519 24949-24949 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-29 18:27:45.520 24949-25081 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 95
                                                                                                      },
                                                                                                      "time": 1.753792065515E9,
                                                                                                      "user_id": "7115A474BB854844",
                                                                                                      "session_id": "0114c9b6-4caf-4f29-85ba-91eb83a852af"
                                                                                                    }
2025-07-29 18:27:45.526 24949-25078 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid 4b1eb19e-a043-4f90-9438-6238eb7b2e84
2025-07-29 18:27:45.531 24949-25089 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:45.531 24949-25089 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"IC":"rv:*,di:*,lv:*,bt:P","branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"f50350e2feeb45cba6f93a01be8229a9","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","experiments":"[]","flavor":"Publish","osVersion":"Android 15","sessionID":"8c744324-5c2c-4a94-8e2a-5901e33251fa","versionCode":"972109401","world_template_id":"00000000-0000-0000-0000-000000000000_0.0.0"}}
2025-07-29 18:27:45.533 24949-24949 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'cf18c93', fd=159
2025-07-29 18:27:45.545 24949-25089 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:45.546 24949-25089 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-29 18:27:45.547 24949-25089 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-29 18:27:45.547 24949-25089 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-29 18:27:45.548 24949-25089 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-29 18:27:45.548 24949-25089 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-29 18:27:45.548 24949-25089 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; retrying event /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp 2 more time(s) after 1000 ms
2025-07-29 18:27:45.555 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"se","data":{"d":95},"time":1.753792065515E9,"user_id":"7115A474BB854844","session_id":"0114c9b6-4caf-4f29-85ba-91eb83a852af"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-29 18:27:45.555 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:27:45.557 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='0114c9b6-4caf-4f29-85ba-91eb83a852af', eventType='SESSION_ENDED'}'
2025-07-29 18:27:45.558 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-29 18:27:45.558 24949-25081 Braze v24.3.0 .u        org.levimc.launcher                  I  New session created with ID: 4035d07b-6ad9-497a-8736-6a954a5f6cfd
2025-07-29 18:27:45.558 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.d5 fired: SessionCreatedEvent(session=
                                                                                                    MutableSession(sessionId=4035d07b-6ad9-497a-8736-6a954a5f6cfd, startTime=1.753792065558E9, endTime=null, isSealed=false, duration=-1))
2025-07-29 18:27:45.558 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.d5 on 2 subscribers.
2025-07-29 18:27:45.558 24949-25081 Braze v24.3.0 .z0       org.levimc.launcher                  D  Session start event for new session received.
2025-07-29 18:27:45.559 24949-25081 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 4035d07b-6ad9-497a-8736-6a954a5f6cfd
2025-07-29 18:27:45.559 24949-25081 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 4035d07b-6ad9-497a-8736-6a954a5f6cfd
2025-07-29 18:27:45.559 24949-25081 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "ss",
                                                                                                      "data": {},
                                                                                                      "time": 1.753792065558E9,
                                                                                                      "user_id": "7115A474BB854844",
                                                                                                      "session_id": "4035d07b-6ad9-497a-8736-6a954a5f6cfd"
                                                                                                    }
2025-07-29 18:27:45.560 24949-25077 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid a747c49a-ab89-499a-b9d1-020d54938ea3
2025-07-29 18:27:45.561 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"ss","data":{},"time":1.753792065558E9,"user_id":"7115A474BB854844","session_id":"4035d07b-6ad9-497a-8736-6a954a5f6cfd"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-29 18:27:45.561 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:27:45.563 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = FLUSH_PENDING_BRAZE_EVENTS
                                                                                                    brazeEvent = null
                                                                                                    sessionId = 4035d07b-6ad9-497a-8736-6a954a5f6cfd
                                                                                                    brazeRequest = null
2025-07-29 18:27:45.563 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:27:45.564 24949-25081 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: true
2025-07-29 18:27:45.564 24949-25081 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-29 18:27:45.564 24949-25081 Braze v24.3.0 .l0       org.levimc.launcher                  V  Device object cache cleared.
2025-07-29 18:27:45.564 24949-25081 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting trigger refresh.
2025-07-29 18:27:45.565 24949-25070 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 312399441; UID 10637; state: ENABLED
2025-07-29 18:27:45.567 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_server_target'. Using default value 'PROD'.
2025-07-29 18:27:45.567 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_server_target' and value: 'PROD'
2025-07-29 18:27:45.570 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:27:45.570 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:27:45.571 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.y5 fired: TriggerDispatchStartedEvent(request={
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:45.571 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.y5 on 1 subscribers.
2025-07-29 18:27:45.571 24949-25081 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:27:45.571 24949-25081 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-29 18:27:45.572 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_automatic_geofence_requests_enabled'. Using default value 'true'.
2025-07-29 18:27:45.572 24949-25081 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_automatic_geofence_requests_enabled' and value: 'true'
2025-07-29 18:27:45.573 24949-25081 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting Braze Geofence refresh on session created event due to configuration.
2025-07-29 18:27:45.573 24949-25081 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Content Card refresh on session created event due to server configuration.
2025-07-29 18:27:45.573 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_sdk_authentication_enabled'. Using default value 'false'.
2025-07-29 18:27:45.573 24949-25081 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Feature Flags refresh on session created event due to server configuration.
2025-07-29 18:27:45.573 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='4035d07b-6ad9-497a-8736-6a954a5f6cfd', eventType='SESSION_STARTED'}'
2025-07-29 18:27:45.573 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-29 18:27:45.573 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_authentication_enabled' and value: 'false'
2025-07-29 18:27:45.574 24949-25078 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-29 18:27:45.574 24949-25081 Braze v24.3.0 .u        org.levimc.launcher                  D  Creating a session seal alarm with a delay of 10000 ms
2025-07-29 18:27:45.575 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_sdk_flavor'. Using default value 'null'.
2025-07-29 18:27:45.575 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_flavor' and value: 'null'
2025-07-29 18:27:45.580 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.i5 fired: bo.app.i5@1f71b1e
2025-07-29 18:27:45.581 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.i5 on 1 subscribers.
2025-07-29 18:27:45.581 24949-25081 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-29 18:27:45.581 24949-25081 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:27:45.581 24949-25081 Braze v24.3.0 .u        org.levimc.launcher                  D  Closed session with id 4035d07b-6ad9-497a-8736-6a954a5f6cfd
2025-07-29 18:27:45.583 24949-25081 Braze v24.3.0 .Braze    org.levimc.launcher                  V  requestImmediateDataFlush() called
2025-07-29 18:27:45.586 24949-25081 Braze v24.3.0 .a5       org.levimc.launcher                  V  Not allowing server config info unlock. Returning null.
2025-07-29 18:27:45.587 24949-25078 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@7addfff
2025-07-29 18:27:45.589 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844"
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:27:45.589 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:27:45.589 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_device_object_whitelisting_enabled'. Using default value 'false'.
2025-07-29 18:27:45.589 24949-25081 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844"
                                                                                                      }
                                                                                                    }
2025-07-29 18:27:45.589 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_device_object_whitelisting_enabled' and value: 'false'
2025-07-29 18:27:45.589 24949-25081 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Not requesting geofences.
2025-07-29 18:27:45.590 24949-25078 Braze v24.3.0 .l0       org.levimc.launcher                  V  Sending full device due to NOTIFICATIONS_ENABLED true
2025-07-29 18:27:45.591 24949-25078 Braze v24.3.0 .l0       org.levimc.launcher                  V  Remote Notification setting changed to true. Updating user subscription.
2025-07-29 18:27:45.598 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_device_object_whitelisting_enabled'. Using default value 'false'.
2025-07-29 18:27:45.598 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_device_object_whitelisting_enabled' and value: 'false'
2025-07-29 18:27:45.599 24949-25078 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-29 18:27:45.599 24949-25078 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@d8241cc
2025-07-29 18:27:45.600 24949-25078 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-29 18:27:45.602 24949-25078 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"ss","data":{},"time":1.753792065558E9,"user_id":"7115A474BB854844","session_id":"4035d07b-6ad9-497a-8736-6a954a5f6cfd"} with uid: a747c49a-ab89-499a-b9d1-020d54938ea3
2025-07-29 18:27:45.602 24949-25078 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"se","data":{"d":95},"time":1.753792065515E9,"user_id":"7115A474BB854844","session_id":"0114c9b6-4caf-4f29-85ba-91eb83a852af"} with uid: 4b1eb19e-a043-4f90-9438-6238eb7b2e84
2025-07-29 18:27:45.603 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_internal_sdk_metadata'. Using default value '[]'.
2025-07-29 18:27:45.603 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_internal_sdk_metadata' and value: '[]'
2025-07-29 18:27:45.604 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_sdk_metadata'. Using default value '[]'.
2025-07-29 18:27:45.604 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-29 18:27:45.604 24949-25078 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-29 18:27:45.605 24949-25078 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-29 18:27:45.607 24949-25078 Braze v24.3.0 .l0       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-29 18:27:45.607 24949-25078 Braze v24.3.0 .m6       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-29 18:27:45.607 24949-25078 Braze v24.3.0 .t0       org.levimc.launcher                  D  Short circuiting execution of network request and immediately marking it as succeeded.
2025-07-29 18:27:45.608 24949-25078 Braze v24.3.0 .j0       org.levimc.launcher                  D  DataSyncRequest executed successfully.
2025-07-29 18:27:45.608 24949-25078 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792065,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844"
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:45.608 24949-25078 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-29 18:27:45.622 24949-25059 BitmapFactory           org.levimc.launcher                  E  Unable to decode file: java.io.FileNotFoundException: /data/user/0/org.levimc.launcher/games/com.mojang/minecraftpe/custom.png: open failed: ENOENT (No such file or directory)
2025-07-29 18:27:45.623 24949-25059 System.err              org.levimc.launcher                  W  getImageData: Could not open image /data/user/0/org.levimc.launcher/games/com.mojang/minecraftpe/custom.png
2025-07-29 18:27:45.631 24949-25080 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "c1cc85fda783f1f2"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "X-Braze-TriggersRequest" => "true"
                                                                                                    "X-Braze-DataRequest" => "true"
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792065,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792065558E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4035d07b-6ad9-497a-8736-6a954a5f6cfd"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 95
                                                                                                          },
                                                                                                          "time": 1.753792065515E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "0114c9b6-4caf-4f29-85ba-91eb83a852af"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:27:45.679 24949-25072 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-29 18:27:45.679 24949-25072 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=272006855601905(auto) mPendingTransactions.size=0 graphicBufferId=107155139067943 transform=7
2025-07-29 18:27:45.802 24949-25080 Braze v24.3.0 .q5       org.levimc.launcher                  V  Enabling SSL protocols: [TLSv1.2, TLSv1.3]
2025-07-29 18:27:46.270 24949-25080 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = c1cc85fda783f1f2 time = 639ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-29 18:27:46.273 24949-25080 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-29 18:27:46.273 24949-25080 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-29 18:27:46.273 24949-25080 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-29 18:27:46.274 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792065,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792065558E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4035d07b-6ad9-497a-8736-6a954a5f6cfd"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 95
                                                                                                          },
                                                                                                          "time": 1.753792065515E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "0114c9b6-4caf-4f29-85ba-91eb83a852af"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-29 18:27:46.275 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-29 18:27:46.275 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-29 18:27:46.276 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792065,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792065558E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4035d07b-6ad9-497a-8736-6a954a5f6cfd"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 95
                                                                                                          },
                                                                                                          "time": 1.753792065515E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "0114c9b6-4caf-4f29-85ba-91eb83a852af"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:46.276 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-29 18:27:46.276 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:27:46.276 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-29 18:27:46.276 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-29 18:27:46.277 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-29 18:27:46.277 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-29 18:27:46.277 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-29 18:27:46.277 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:27:46.277 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:27:46.277 24949-25080 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 7115A474BB854844
2025-07-29 18:27:46.278 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792065,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792065558E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4035d07b-6ad9-497a-8736-6a954a5f6cfd"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 95
                                                                                                          },
                                                                                                          "time": 1.753792065515E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "0114c9b6-4caf-4f29-85ba-91eb83a852af"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:46.278 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-29 18:27:46.279 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792065,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792065558E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4035d07b-6ad9-497a-8736-6a954a5f6cfd"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 95
                                                                                                          },
                                                                                                          "time": 1.753792065515E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "0114c9b6-4caf-4f29-85ba-91eb83a852af"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:46.280 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-29 18:27:46.280 24949-25080 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@7addfff
2025-07-29 18:27:46.280 24949-25080 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@d8241cc
2025-07-29 18:27:46.281 24949-25080 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-29 18:27:46.281 24949-25080 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-29 18:27:46.281 24949-25080 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-29 18:27:46.282 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  D  Trigger dispatch completed. Alerting subscribers.
2025-07-29 18:27:46.282 24949-25077 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid a747c49a-ab89-499a-b9d1-020d54938ea3
2025-07-29 18:27:46.282 24949-25077 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 4b1eb19e-a043-4f90-9438-6238eb7b2e84
2025-07-29 18:27:46.282 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.x5 fired: TriggerDispatchCompletedEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792065,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792065558E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4035d07b-6ad9-497a-8736-6a954a5f6cfd"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 95
                                                                                                          },
                                                                                                          "time": 1.753792065515E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "0114c9b6-4caf-4f29-85ba-91eb83a852af"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:46.283 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.x5 on 1 subscribers.
2025-07-29 18:27:46.283 24949-25080 Braze v24.....bo.app.d6 org.levimc.launcher                  D  In flight trigger requests is empty. Executing any pending trigger events.
2025-07-29 18:27:46.549 24949-25089 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:46.550 24949-25089 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"IC":"rv:*,di:*,lv:*,bt:P","branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"f50350e2feeb45cba6f93a01be8229a9","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","experiments":"[]","flavor":"Publish","osVersion":"Android 15","sessionID":"8c744324-5c2c-4a94-8e2a-5901e33251fa","versionCode":"972109401","world_template_id":"00000000-0000-0000-0000-000000000000_0.0.0"}}
2025-07-29 18:27:46.550 24949-25089 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:46.550 24949-25089 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-29 18:27:46.551 24949-25089 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-29 18:27:46.551 24949-25089 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-29 18:27:46.551 24949-25089 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-29 18:27:46.551 24949-25089 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-29 18:27:46.551 24949-25089 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; retrying event /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp 1 more time(s) after 1000 ms
2025-07-29 18:27:46.565 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:27:46.565 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:27:46.566 24949-25080 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:27:46.566 24949-25078 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-29 18:27:46.568 24949-25078 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@7addfff
2025-07-29 18:27:46.569 24949-25078 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@d8241cc
2025-07-29 18:27:46.570 24949-25078 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-29 18:27:46.583 24949-25080 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "bc0433fb73b8332c"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792066,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:27:46.983 24949-25080 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = bc0433fb73b8332c time = 400ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-29 18:27:46.984 24949-25080 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-29 18:27:46.984 24949-25080 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-29 18:27:46.984 24949-25080 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-29 18:27:46.984 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792066,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-29 18:27:46.985 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-29 18:27:46.985 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-29 18:27:46.985 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792066,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:46.985 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-29 18:27:46.985 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:27:46.986 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-29 18:27:46.986 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-29 18:27:46.986 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-29 18:27:46.986 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-29 18:27:46.986 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-29 18:27:46.986 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:27:46.986 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:27:46.986 24949-25080 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 7115A474BB854844
2025-07-29 18:27:46.987 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792066,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:46.987 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-29 18:27:46.988 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792066,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:46.988 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-29 18:27:46.989 24949-25080 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@7addfff
2025-07-29 18:27:46.989 24949-25080 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@d8241cc
2025-07-29 18:27:46.989 24949-25080 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-29 18:27:46.989 24949-25080 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-29 18:27:47.552 24949-25089 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:47.553 24949-25089 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"IC":"rv:*,di:*,lv:*,bt:P","branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"f50350e2feeb45cba6f93a01be8229a9","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","experiments":"[]","flavor":"Publish","osVersion":"Android 15","sessionID":"8c744324-5c2c-4a94-8e2a-5901e33251fa","versionCode":"972109401","world_template_id":"00000000-0000-0000-0000-000000000000_0.0.0"}}
2025-07-29 18:27:47.553 24949-25089 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:47.554 24949-25089 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-29 18:27:47.554 24949-25089 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-29 18:27:47.555 24949-25089 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-29 18:27:47.555 24949-25089 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-29 18:27:47.555 24949-25089 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-29 18:27:47.556 24949-25089 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; retrying event /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp 0 more time(s) after 1000 ms
2025-07-29 18:27:48.557 24949-25089 MCPE                    org.levimc.launcher                  I  CrashManager: uploading /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:48.557 24949-25089 MCPE                    org.levimc.launcher                  D  CrashManager: sentry parameters: {"release":"1.21.94","tags":{"IC":"rv:*,di:*,lv:*,bt:P","branch":"r/21_u9","commit":"91dbc42aab4a2fb3b48db66d44ddc975ae751a3d","cpuCores":"8","cpuFeatures":"fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp","cpuName":"<unknown>","cpuType":"arm64-v8a","deviceID":"f50350e2feeb45cba6f93a01be8229a9","deviceName":"SAMSUNG SM-A166P","dynamicTexturesEnabled":"true","experiments":"[]","flavor":"Publish","osVersion":"Android 15","sessionID":"8c744324-5c2c-4a94-8e2a-5901e33251fa","versionCode":"972109401","world_template_id":"00000000-0000-0000-0000-000000000000_0.0.0"}}
2025-07-29 18:27:48.558 24949-25089 MCPE                    org.levimc.launcher                  W  CrashManager: Error uploading dump file: /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:48.558 24949-25089 System.err              org.levimc.launcher                  W  java.lang.RuntimeException: Stub!
2025-07-29 18:27:48.558 24949-25089 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.AbstractHttpClient.<init>(AbstractHttpClient.java:37)
2025-07-29 18:27:48.559 24949-25089 System.err              org.levimc.launcher                  W  	at org.apache.http.impl.client.DefaultHttpClient.<init>(DefaultHttpClient.java:39)
2025-07-29 18:27:48.559 24949-25089 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadDump(CrashManager.java:280)
2025-07-29 18:27:48.559 24949-25089 System.err              org.levimc.launcher                  W  	at com.mojang.minecraftpe.CrashManager.uploadCrashFile(CrashManager.java:202)
2025-07-29 18:27:48.560 24949-25089 MCPE                    org.levimc.launcher                  E  An error occurred uploading an event:"Stub!"; dropping event /data/user/0/org.levimc.launcher/crash/8c744324-5c2c-4a94-8e2a-5901e33251fa.dmp
2025-07-29 18:27:48.797 24949-25059 fmod                    org.levimc.launcher                  I  AudioDevice::init : Min buffer size: 8224 bytes
2025-07-29 18:27:48.797 24949-25059 fmod                    org.levimc.launcher                  I  AudioDevice::init : Actual buffer size: 8224 bytes
2025-07-29 18:27:48.826 24949-25059 AudioTrack              org.levimc.launcher                  W  Use of stream types is deprecated for operations other than volume control
2025-07-29 18:27:48.826 24949-25059 AudioTrack              org.levimc.launcher                  W  See the documentation of AudioTrack() for what to use instead with android.media.AudioAttributes to qualify your playback use case
2025-07-29 18:27:48.978 24949-24949 XALJAVA                 org.levimc.launcher                  V  [PresenceManager] XalLogger created.
2025-07-29 18:27:48.987 24949-24949 XALJAVA                 org.levimc.launcher                  W  [P][PresenceManager] Ignoring resume, not currently paused
2025-07-29 18:27:48.991 24949-24949 HttpCallStaticGlue      org.levimc.launcher                  D  Successfully registerered HttpCall methods
2025-07-29 18:27:48.991 24949-24949 XboxLiveAppConfig       org.levimc.launcher                  D  Successfully registerered XboxLiveAppConfig methods
2025-07-29 18:27:48.991 24949-24949 XSAPI.Android           org.levimc.launcher                  D  Successfully registerered HttpCall tcuiMethods
2025-07-29 18:27:48.993 24949-24949 Interop                 org.levimc.launcher                  I  locale is: en_GB
2025-07-29 18:27:49.077 24949-25071 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Lcom/android/org/conscrypt/OpenSSLProvider;-><init>()V (runtime_flags=CorePlatformApi, domain=core-platform, api=unsupported,core-platform-api) from Lorg/spongycastle/jcajce/provider/drbg/DRBG; (domain=app) using reflection: allowed
2025-07-29 18:27:49.079 24949-25071 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Lcom/android/org/conscrypt/OpenSSLRandom;-><init>()V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/spongycastle/jcajce/provider/drbg/DRBG; (domain=app) using reflection: allowed
2025-07-29 18:27:49.108 24949-25059 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Async Services Manager starting in Async mode
2025-07-29 18:27:49.461 24949-25059 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using External dir (NEW) - CurrentFileStoragePath is now '/storage/emulated/0/Android/data/org.levimc.launcher/files'
2025-07-29 18:27:49.470 24949-25059 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using AppData dir - CurrentFileStoragePath is now '/data/user/0/org.levimc.launcher'
2025-07-29 18:27:49.476 24949-24957 levimc.launcher         org.levimc.launcher                  I  NativeAlloc concurrent mark compact GC freed 7509KB AllocSpace bytes, 60(1728KB) LOS objects, 43% free, 30MB/54MB, paused 344us,10.124ms total 56.874ms
2025-07-29 18:27:49.477 24949-24959 System                  org.levimc.launcher                  W  A resource failed to call ZipFile.close. 
2025-07-29 18:27:49.479 24949-24958 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=154
2025-07-29 18:27:49.484 24949-24958 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=145
2025-07-29 18:27:49.501 24949-24959 System                  org.levimc.launcher                  W  A resource failed to call close. 
2025-07-29 18:27:51.733 24949-25059 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Time played notifier not required for 'en_GB'
2025-07-29 18:27:54.012 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:27:54.012 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[Launcher]@790a5e5
2025-07-29 18:27:54.140 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:27:55.578 24949-25077 Braze v24....eCoroutine org.levimc.launcher                  D  Requesting data flush on internal session close flush timer.
2025-07-29 18:27:55.581 24949-25081 Braze v24.3.0 .Braze    org.levimc.launcher                  V  requestImmediateDataFlush() called
2025-07-29 18:27:55.589 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:27:55.589 24949-25081 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:27:55.591 24949-25081 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:27:55.591 24949-25077 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-29 18:27:55.595 24949-25077 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@7addfff
2025-07-29 18:27:55.596 24949-25077 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@d8241cc
2025-07-29 18:27:55.596 24949-25077 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-29 18:27:55.601 24949-25080 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "4fec441b89c56c0"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792075,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:27:55.911 24949-25080 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = 4fec441b89c56c0 time = 310ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-29 18:27:55.911 24949-25080 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-29 18:27:55.911 24949-25080 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-29 18:27:55.911 24949-25080 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-29 18:27:55.912 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792075,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-29 18:27:55.912 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-29 18:27:55.912 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-29 18:27:55.912 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792075,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:55.912 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-29 18:27:55.912 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:27:55.913 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-29 18:27:55.913 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-29 18:27:55.913 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-29 18:27:55.913 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-29 18:27:55.913 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-29 18:27:55.913 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:27:55.913 24949-25080 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:27:55.914 24949-25080 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 7115A474BB854844
2025-07-29 18:27:55.915 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792075,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:55.915 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-29 18:27:55.915 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792075,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124034.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:27:55.916 24949-25080 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-29 18:27:55.916 24949-25080 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@7addfff
2025-07-29 18:27:55.916 24949-25080 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@d8241cc
2025-07-29 18:27:55.917 24949-25080 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-29 18:27:55.917 24949-25080 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-29 18:27:56.946 24949-24957 levimc.launcher         org.levimc.launcher                  I  NativeAlloc concurrent mark compact GC freed 7026KB AllocSpace bytes, 7(264KB) LOS objects, 43% free, 31MB/55MB, paused 780us,13.486ms total 64.951ms
2025-07-29 18:27:57.117 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:27:57.141 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=no preference, reason=boost timeout, vri=VRI[Launcher]@790a5e5
2025-07-29 18:27:57.183 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[Launcher]@790a5e5
2025-07-29 18:27:57.225 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:27:57.487 24949-25081 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Received request to change current user 7115A474BB854844 to the same user id. Not changing user.
2025-07-29 18:27:58.553 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:27:58.638 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:27:59.058 24949-25066 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [24949] IntegrityService : requestIntegrityToken(IntegrityTokenRequest{nonce=MjBDQTIuNTBDNjczQjdBNERFQzM1QQ, cloudProjectNumber=null})
2025-07-29 18:27:59.064 24949-25211 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [24949] IntegrityService : Initiate binding to the service.
2025-07-29 18:27:59.077 24949-24949 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [24949] IntegrityService : ServiceConnectionImpl.onServiceConnected(ComponentInfo{com.android.vending/com.google.android.finsky.integrityservice.IntegrityService})
2025-07-29 18:27:59.080 24949-25211 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [24949] IntegrityService : linkToDeath
2025-07-29 18:27:59.923 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:27:59.987 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:00.834  3729-4000  Finsky                  com.android.vending                  E  [1792] requestIntegrityToken() failed for org.levimc.launcher.
                                                                                                    com.google.android.finsky.integrityservice.IntegrityException
                                                                                                    	at yyr.a(PG:82)
                                                                                                    	at yyp.iK(PG:22)
                                                                                                    	at maa.j(PG:9)
                                                                                                    	at mzz.j(PG:22)
                                                                                                    	at lzu.run(PG:31)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
                                                                                                    Caused by: DisplayErrorMessage[Error while retrieving information from server. DF-DFERH-01]
                                                                                                    	at mzz.kq(PG:40)
                                                                                                    	at lzy.a(PG:204)
                                                                                                    	at lzy.run(PG:6)
2025-07-29 18:28:00.838 24949-24968 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [24949] OnRequestIntegrityTokenCallback : onRequestIntegrityToken
2025-07-29 18:28:00.841 24949-25211 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [24949] IntegrityService : Unbind from service.
2025-07-29 18:28:01.068 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:01.179 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:02.043 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:02.176 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:02.420 24949-25069 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Opening level '/data/user/0/org.levimc.launcher/games/com.mojang/minecraftWorlds/f9F86PQb7VY=/db'
2025-07-29 18:28:02.462 24949-25069 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - [SERVER] Pack Stack - None
2025-07-29 18:28:05.176 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=no preference, reason=boost timeout, vri=VRI[Launcher]@790a5e5
2025-07-29 18:28:06.919 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@790a5e5 mNativeObject= 0xb4000078f2eebc00 sc.mNativeObject= 0xb4000078e1e21380 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:28:06.919 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=1 res=0x0 s={true 0xb4000078e230f800} ch=false seqId=0
2025-07-29 18:28:06.920 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  updateBoundsLayer: t=android.view.SurfaceControl$Transaction@2b75e22 sc=Surface(name=Bounds for - org.levimc.launcher/com.mojang.minecraftpe.Launcher@0)/@0x70da0b3 frame=5
2025-07-29 18:28:06.921 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-29 18:28:06.928 24949-25009 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  mWNT: t=0xb400007859714f00 mBlastBufferQueue=0xb4000078f2eebc00 fn= 5 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-29 18:28:06.982 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@790a5e5 mNativeObject= 0xb4000078f2eebc00 sc.mNativeObject= 0xb4000078e1e21380 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:28:06.983 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=0 res=0x0 s={true 0xb4000078e230f800} ch=false seqId=0
2025-07-29 18:28:06.983 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  updateBoundsLayer: t=android.view.SurfaceControl$Transaction@2b75e22 sc=Surface(name=Bounds for - org.levimc.launcher/com.mojang.minecraftpe.Launcher@0)/@0x70da0b3 frame=6
2025-07-29 18:28:06.984 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-29 18:28:06.987 24949-25010 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  mWNT: t=0xb400007859715380 mBlastBufferQueue=0xb4000078f2eebc00 fn= 6 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-29 18:28:07.074 24949-25328 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Player connected: Kai, xuid: 
2025-07-29 18:28:18.741 24949-25328 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Player Spawned: Kai xuid: , pfid: 7115a474bb854844
2025-07-29 18:28:23.755 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:23.755 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[Launcher]@790a5e5
2025-07-29 18:28:23.834 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:25.169 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:25.306 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:25.335 24949-24949 InputMethodManager      org.levimc.launcher                  I  invalidateInput
2025-07-29 18:28:25.336 24949-24949 InputMethodManager      org.levimc.launcher                  I  invalidateInput
2025-07-29 18:28:25.339 24949-24949 InputMethodManager_LC   org.levimc.launcher                  I  showSoftInput(View,I)
2025-07-29 18:28:25.345 24949-24949 ImeTracker              org.levimc.launcher                  I  org.levimc.launcher:4abf3161: onRequestShow at ORIGIN_CLIENT reason SHOW_SOFT_INPUT fromUser false
2025-07-29 18:28:25.347 24949-24949 InputMethodManager_LC   org.levimc.launcher                  I  ssi(): flags=0 view=org.levimc.launcher reason=SHOW_SOFT_INPUT
2025-07-29 18:28:25.348 24949-24949 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-29 18:28:25.348 24949-24949 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-29 18:28:25.350 24949-24949 InputMethodManager      org.levimc.launcher                  D  showSoftInput() view=com.mojang.minecraftpe.TextInputProxyEditTextbox{778dfaa VFED..CL. .F....I. 0,0-0,0 aid=1073741824} flags=0 reason=SHOW_SOFT_INPUT
2025-07-29 18:28:25.352 24949-24949 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@790a5e5 mNativeObject= 0xb4000078f2eebc00 sc.mNativeObject= 0xb4000078e1e21380 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:28:25.352 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=0 res=0x0 s={true 0xb4000078e230f800} ch=false seqId=0
2025-07-29 18:28:25.353 24949-25022 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=247
2025-07-29 18:28:25.368 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-29 18:28:25.383 24949-25009 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  mWNT: t=0xb400007885a85480 mBlastBufferQueue=0xb4000078f2eebc00 fn= 7 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-29 18:28:25.393 24949-24949 AssistStructure         org.levimc.launcher                  I  Flattened final assist data: 836 bytes, containing 1 windows, 4 views
2025-07-29 18:28:25.433 24949-24957 levimc.launcher         org.levimc.launcher                  I  NativeAlloc concurrent mark compact GC freed 17MB AllocSpace bytes, 11(220KB) LOS objects, 42% free, 32MB/56MB, paused 879us,10.074ms total 76.311ms
2025-07-29 18:28:25.433 24949-24958 BpBinder                org.levimc.launcher                  I  onLastStrongRef automatically unlinking death recipients: 
2025-07-29 18:28:25.574 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[96,1080][2205,1080] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:28:25.576 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:28:25.593 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[96,483][2205,1080] mVisible=false mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:28:25.594 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:28:25.643 24949-24949 InsetsController        org.levimc.launcher                  D  show(ime(), fromIme=true)
2025-07-29 18:28:25.645 24949-24949 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=true, mask=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.show:1340 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:8056 android.view.ViewRootImpl$ViewRootHandler.handleMessage:7991 android.os.Handler.dispatchMessage:107 android.os.Looper.loopOnce:257 android.os.Looper.loop:342 android.app.ActivityThread.main:9638 
2025-07-29 18:28:25.648 24949-24949 InsetsController        org.levimc.launcher                  I  controlAnimationUncheckedInner: Added types=ime, animType=0, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 
2025-07-29 18:28:25.653 24949-24949 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetstrue
2025-07-29 18:28:25.653 24949-24949 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:28:25.661 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[96,483][2205,1080] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:28:25.661 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:28:25.666 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[96,483][2205,1080] mVisible=true mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:28:25.667 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:28:25.682 24949-24949 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetstrue
2025-07-29 18:28:25.682 24949-24949 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:28:25.952 24949-24949 InsetsController        org.levimc.launcher                  I  cancelAnimation: types=ime, animType=0, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationThreadControlRunner$1.lambda$notifyFinished$0:87 android.view.InsetsAnimationThreadControlRunner$1.$r8$lambda$cDFF0h4Ncq-8EXdGszv69jrUu7c:0 
2025-07-29 18:28:25.954 24949-24949 ImeTracker              org.levimc.launcher                  I  org.levimc.launcher:4abf3161: onShown
2025-07-29 18:28:28.307 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=no preference, reason=boost timeout, vri=VRI[Launcher]@790a5e5
2025-07-29 18:28:28.330 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:28.330 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[Launcher]@790a5e5
2025-07-29 18:28:28.363 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:28.370 24949-24949 InputMethodManager      org.levimc.launcher                  I  invalidateInput
2025-07-29 18:28:28.373 24949-24949 ImeTracker              org.levimc.launcher                  I  org.levimc.launcher:ed2cfe28: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT fromUser false
2025-07-29 18:28:28.373 24949-24949 InputMethodManager_LC   org.levimc.launcher                  I  hsifw() - flag : 0
2025-07-29 18:28:28.374 24949-24949 InputMethodManager_LC   org.levimc.launcher                  I  hsifw() - mService.hideSoftInput
2025-07-29 18:28:28.377 24949-24949 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-29 18:28:28.378 24949-24949 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-29 18:28:28.385 24949-25086 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=235
2025-07-29 18:28:28.386 24949-24949 InsetsController        org.levimc.launcher                  D  hide(ime(), fromIme=true)
2025-07-29 18:28:28.386 24949-24949 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.ViewRootImpl$ViewRootHandler.handleMessageImpl:8063 android.view.ViewRootImpl$ViewRootHandler.handleMessage:7991 android.os.Handler.dispatchMessage:107 android.os.Looper.loopOnce:257 android.os.Looper.loop:342 android.app.ActivityThread.main:9638 
2025-07-29 18:28:28.387 24949-24949 InsetsController        org.levimc.launcher                  I  controlAnimationUncheckedInner: Added types=ime, animType=1, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 
2025-07-29 18:28:28.392 24949-24949 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:28:28.392 24949-24949 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:28:28.405 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[96,483][2205,1080] mVisible=false mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:28:28.405 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:28:28.429 24949-24949 InsetsController        org.levimc.launcher                  I  controlAnimationUncheckedInner: Added types=navigationBars, animType=1, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1502 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 
2025-07-29 18:28:28.442 24949-24949 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:28:28.443 24949-24949 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:28:28.445 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[96,483][2205,1080] mVisible=false mFlags= mSideHint=BOTTOM mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:28:28.446 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:28:28.679 24949-24949 InsetsController        org.levimc.launcher                  I  cancelAnimation: types=ime, animType=1, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationThreadControlRunner$1.lambda$notifyFinished$0:87 android.view.InsetsAnimationThreadControlRunner$1.$r8$lambda$cDFF0h4Ncq-8EXdGszv69jrUu7c:0 
2025-07-29 18:28:28.681 24949-24949 ImeTracker              org.levimc.launcher                  I  org.levimc.launcher:c1a9f5fb: onRequestHide at ORIGIN_CLIENT reason HIDE_SOFT_INPUT_ON_ANIMATION_STATE_CHANGED fromUser false
2025-07-29 18:28:28.682 24949-24949 InputMethodManager_LC   org.levimc.launcher                  I  notifyImeHidden: IInputMethodManagerGlobalInvoker.hideSoftInput
2025-07-29 18:28:28.684 24949-24949 ImeTracker              org.levimc.launcher                  I  org.levimc.launcher:ed2cfe28: onHidden
2025-07-29 18:28:28.793 24949-24949 InsetsController        org.levimc.launcher                  I  cancelAnimation: types=navigationBars, animType=1, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.notifyFinished:1890 android.view.InsetsAnimationThreadControlRunner$1.lambda$notifyFinished$0:87 android.view.InsetsAnimationThreadControlRunner$1.$r8$lambda$cDFF0h4Ncq-8EXdGszv69jrUu7c:0 
2025-07-29 18:28:28.886 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:28.984 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:29.200 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:28:29.200 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:28:29.207 24949-24949 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:28:29.208 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:28:29.671 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:29.768 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:30.751 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:30.829 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:32.051 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:28:32.162 24949-24949 VRI[Launcher]@790a5e5   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:28:32.212 24949-25059 Bedrock                 org.levimc.launcher                  W  Breakpad _filterCallback called
---------------------------- PROCESS STARTED (25355) for package org.levimc.launcher ----------------------------
2025-07-29 18:28:32.651 24949-25012 levimc.launcher         org.levimc.launcher                  E  QUEUE_BUFFER_TIMEOUT: surfaceName: SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher, fenceName: GPU completion, lastDuration: 0, waitFenceTime: 398
2025-07-29 18:28:32.690 24949-25059 Bedrock                 org.levimc.launcher                  W  Breakpad _dumpCallback called, dump path: /data/user/0/org.levimc.launcher/crash/77e8b07f-945d-41a8-ae58-579e9a5b8bcd.dmp
---------------------------- PROCESS ENDED (24949) for package org.levimc.launcher ----------------------------
---------------------------- PROCESS ENDED (25355) for package org.levimc.launcher ----------------------------
