2025-07-29 18:35:46.319 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:35:46.331 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[MainActivity]@2f3909d
2025-07-29 18:35:46.406 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:35:46.431 27460-27564 LeviLogger              org.levimc.launcher                  I  [LeviMC] Cleaning cache directory...
2025-07-29 18:35:46.431 27460-27564 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/BaseDexClassLoader;->pathList:Ldalvik/system/DexPathList; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:35:46.431 27460-27564 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->addDexPath(Ljava/lang/String;Ljava/io/File;)V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:35:46.432 27460-27564 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: launcher.dex
2025-07-29 18:35:46.433 27460-27564 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: launcher.dex
2025-07-29 18:35:46.444 27460-27460 Dialog                  org.levimc.launcher                  I  mIsDeviceDefault = false, mIsSamsungBasicInteraction = false, isMetaDataInActivity = false
2025-07-29 18:35:46.479 27460-27460 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff010102 d=android.graphics.drawable.InsetDrawable@c122871
2025-07-29 18:35:46.480 27460-27460 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=0 d=android.graphics.drawable.ColorDrawable@4babbc4
2025-07-29 18:35:46.480 27460-27460 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#addView, ty=2, view=com.android.internal.policy.DecorView{64fdaad V.ED..... R.....I. 0,0-0,0}[MainActivity], caller=android.view.WindowManagerImpl.addView:158 android.app.Dialog.show:511 org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading:308 
2025-07-29 18:35:46.483 27460-27460 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-29 18:35:46.484 27460-27493 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-29 18:35:46.493 27460-27460 InputTransport          org.levimc.launcher                  D  Input channel constructed: '7125056', fd=149
2025-07-29 18:35:46.494 27460-27460 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:35:46.494 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-29 18:35:46.494 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@64fdaad IsHRR=false TM=true
2025-07-29 18:35:46.511 27460-27460 BufferQueueConsumer     org.levimc.launcher                  D  [](id:6b4400000002,api:0,p:-1,c:27460) connect: controlledByApp=false
2025-07-29 18:35:46.511 27460-27460 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:0,s:0) constructor()
2025-07-29 18:35:46.511 27460-27460 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[MainActivity]@36f01e2 mNativeObject= 0xb400007a21fed000 sc.mNativeObject= 0xb4000078e9b0f500 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-29 18:35:46.511 27460-27460 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 556 h= 590 mName = VRI[MainActivity]@36f01e2 mNativeObject= 0xb400007a21fed000 sc.mNativeObject= 0xb4000078e9b0f500 format= -2 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-29 18:35:46.511 27460-27460 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:0,s:0) update width=556 height=590 format=-2 mTransformHint=4
2025-07-29 18:35:46.512 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(982,335,1358,745) relayoutAsync=false req=(376,410)0 dur=12 res=0x3 s={true 0xb4000078e9ba4000} ch=true seqId=0
2025-07-29 18:35:46.512 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-29 18:35:46.513 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000078e9ba4000} hwInitialized=true
2025-07-29 18:35:46.514 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-29 18:35:46.514 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[MainActivity]@36f01e2#6
2025-07-29 18:35:46.514 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Creating new active sync group VRI[MainActivity]@36f01e2#7
2025-07-29 18:35:46.515 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-29 18:35:46.517 27460-27519 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-29 18:35:46.517 27460-27519 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  mWNT: t=0xb4000079e9933f00 mBlastBufferQueue=0xb400007a21fed000 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-29 18:35:46.517 27460-27519 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-29 18:35:46.519 27460-27493 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-29 18:35:46.520 27460-27493 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:1,s:0) acquireNextBufferLocked size=556x590 mFrameNumber=1 applyTransaction=true mTimestamp=272487696229318(auto) mPendingTransactions.size=0 graphicBufferId=117939801948188 transform=7
2025-07-29 18:35:46.520 27460-27493 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-29 18:35:46.521 27460-27493 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 27460    Tid : 27493
2025-07-29 18:35:46.521 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-29 18:35:46.522 27460-27493 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-29 18:35:46.536 27460-27564 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes2.dex
2025-07-29 18:35:46.542 27460-27460 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-29 18:35:46.543 27460-27460 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-29 18:35:46.578 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000078e9ba4000}
2025-07-29 18:35:46.579 27460-27460 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-29 18:35:46.579 27460-27460 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-29 18:35:46.583 27460-27470 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=155
2025-07-29 18:35:46.590 27460-27460 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity
2025-07-29 18:35:46.591 27460-27564 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes2.dex
2025-07-29 18:35:46.656 27460-27564 LeviLogger              org.levimc.launcher                  I  [LeviMC] Copied file: classes.dex
2025-07-29 18:35:46.702 27460-27564 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loaded dex: classes.dex
2025-07-29 18:35:46.704 27460-27564 LeviLogger              org.levimc.launcher                  I  [LeviMC] /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64
2025-07-29 18:35:46.704 27460-27564 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:35:46.704 27460-27564 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->nativeLibraryPathElements:[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:35:46.704 27460-27564 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Ldalvik/system/DexPathList;->makePathElements(Ljava/util/List;)[Ldalvik/system/DexPathList$NativeLibraryElement; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:35:46.704 27460-27564 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden field Ldalvik/system/DexPathList;->systemNativeLibraryDirectories:Ljava/util/List; (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/levimc/launcher/core/minecraft/MinecraftLauncher; (domain=app) using reflection: allowed
2025-07-29 18:35:46.712 27460-27567 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libc++_shared.so using class loader ns clns-7 (caller=/data/app/~~zS1X6PfnvDgXY1yQNF_bXw==/org.levimc.launcher-0Gf0DXhSkTJJr-cH3Ta97g==/base.apk!classes7.dex): ok
2025-07-29 18:35:46.714 27460-27567 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libfmod.so using class loader ns clns-7 (caller=/data/app/~~zS1X6PfnvDgXY1yQNF_bXw==/org.levimc.launcher-0Gf0DXhSkTJJr-cH3Ta97g==/base.apk!classes7.dex): ok
2025-07-29 18:35:46.820 27460-27567 nativeloader            org.levimc.launcher                  D  Load /data/app/~~x939dOk30er6H1h-CPX2jA==/com.mojang.minecraftpe-KoCHMvXwB_rSGl6Vdza_bg==/lib/arm64/libminecraftpe.so using class loader ns clns-7 (caller=/data/app/~~zS1X6PfnvDgXY1yQNF_bXw==/org.levimc.launcher-0Gf0DXhSkTJJr-cH3Ta97g==/base.apk!classes7.dex): ok
2025-07-29 18:35:46.820 27460-27567 Minecraft               org.levimc.launcher                  V  Entering JNI_OnLoad 0x7a177635f0
2025-07-29 18:35:46.820 27460-27567 Minecraft               org.levimc.launcher                  V  JNI_OnLoad completed
2025-07-29 18:35:46.820 27460-27567 LeviLogger              org.levimc.launcher                  I  [LeviMC] Loading bundled mods in safe mode (experimental features disabled)...
2025-07-29 18:35:46.820 27460-27567 LeviLogger              org.levimc.launcher                  I  [LeviMC] Starting to load bundled mods...
2025-07-29 18:35:46.820 27460-27567 LeviLogger              org.levimc.launcher                  I  [LeviMC] Attempting to load bundled mod: libCrestHelper.so
2025-07-29 18:35:46.822 27460-27567 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-29 18:35:46.822 27460-27567 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~zS1X6PfnvDgXY1yQNF_bXw==/org.levimc.launcher-0Gf0DXhSkTJJr-cH3Ta97g==/base.apk!/lib/arm64-v8a/libCrestHelper.so
2025-07-29 18:35:46.822 27460-27567 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-29 18:35:46.823 27460-27567 nativeloader            org.levimc.launcher                  D  Load /data/app/~~zS1X6PfnvDgXY1yQNF_bXw==/org.levimc.launcher-0Gf0DXhSkTJJr-cH3Ta97g==/base.apk!/lib/arm64-v8a/libCrestHelper.so using class loader ns clns-7 (caller=/data/app/~~zS1X6PfnvDgXY1yQNF_bXw==/org.levimc.launcher-0Gf0DXhSkTJJr-cH3Ta97g==/base.apk!classes9.dex): ok
2025-07-29 18:35:46.823 27460-27567 LeviLogger              org.levimc.launcher                  I  [LeviMC] Successfully loaded bundled mod: libCrestHelper.so
2025-07-29 18:35:46.823 27460-27567 LeviLogger              org.levimc.launcher                  I  [LeviMC] Library CrestHelper loaded and initialized successfully
2025-07-29 18:35:46.823 27460-27567 LeviLogger              org.levimc.launcher                  I  [LeviMC] Finished loading bundled mods. Loaded 1 libraries successfully.
2025-07-29 18:35:46.881 27460-27460 ActivityThread          org.levimc.launcher                  D  org.levimc.launcher will use render engine as VK
2025-07-29 18:35:46.903 27460-27460 nativeloader            org.levimc.launcher                  D  Load libmaesdk.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libmaesdk.so" not found
2025-07-29 18:35:46.903 27460-27460 MCPE                    org.levimc.launcher                  D  maesdk library not found. This is expected if we're not in Edu mode
2025-07-29 18:35:46.904 27460-27460 nativeloader            org.levimc.launcher                  D  Load libovrfmod.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libovrfmod.so" not found
2025-07-29 18:35:46.905 27460-27460 MCPE                    org.levimc.launcher                  D  OVRfmod library not found
2025-07-29 18:35:46.906 27460-27460 nativeloader            org.levimc.launcher                  D  Load libovrplatformloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/classes2.dex): dlopen failed: library "libovrplatformloader.so" not found
2025-07-29 18:35:46.906 27460-27460 MCPE                    org.levimc.launcher                  D  OVRplatform library not found
2025-07-29 18:35:46.908 27460-27460 GlossHook               org.levimc.launcher                  I  GlossHook v1.9.2, Created by XMDS.
2025-07-29 18:35:46.908 27460-27460 GlossHook               org.levimc.launcher                  I  GlossHook is exist at: /data/app/~~zS1X6PfnvDgXY1yQNF_bXw==/org.levimc.launcher-0Gf0DXhSkTJJr-cH3Ta97g==/base.apk!/lib/arm64-v8a/libpreloader.so
2025-07-29 18:35:46.908 27460-27460 GlossHook               org.levimc.launcher                  I  Gloss Elfinfo init...
2025-07-29 18:35:46.908 27460-27460 nativeloader            org.levimc.launcher                  D  Load /data/app/~~zS1X6PfnvDgXY1yQNF_bXw==/org.levimc.launcher-0Gf0DXhSkTJJr-cH3Ta97g==/base.apk!/lib/arm64-v8a/libpreloader.so using class loader ns clns-7 (caller=/data/user/0/org.levimc.launcher/code_cache/dex/launcher.dex): ok
2025-07-29 18:35:46.938 27460-27460 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Landroid/content/res/AssetManager;->addAssetPath(Ljava/lang/String;)I (runtime_flags=0, domain=platform, api=unsupported) from Lcom/mojang/minecraftpe/Launcher; (domain=app) using reflection: allowed
2025-07-29 18:35:46.956 27460-27460 MinecraftPlatform       org.levimc.launcher                  I  MainActivity::onCreate
2025-07-29 18:35:46.961 27460-27460 DecorView               org.levimc.launcher                  I  setWindowBackground: isPopOver=false color=ff303030 d=android.graphics.drawable.ColorDrawable@5cc12c1
2025-07-29 18:35:46.964 27460-27460 gti.InputConnection     org.levimc.launcher                  D  InputConnection created
2025-07-29 18:35:46.968 27460-27460 GameActivity            org.levimc.launcher                  I  Looking for library libpreloader.so
2025-07-29 18:35:46.968 27460-27460 GameActivity            org.levimc.launcher                  I  Found library libpreloader.so. Loading...
2025-07-29 18:35:46.971 27460-27460 GameActivity            org.levimc.launcher                  D  GameActivity_register
2025-07-29 18:35:46.971 27460-27460 GameActivity            org.levimc.launcher                  D  SDK version: 35
2025-07-29 18:35:46.972 27460-27571 Minecraft               org.levimc.launcher                  I  android_main starting. internalDataPath is '/data/user/0/org.levimc.launcher/files', externalDataPath is '/storage/emulated/0/Android/data/org.levimc.launcher/files'
2025-07-29 18:35:46.976 27460-27571 MCPE                    org.levimc.launcher                  E  *** setCachedDeviceId(f50350e2feeb45cba6f93a01be8229a9)
2025-07-29 18:35:46.978 27460-27571 Bedrock                 org.levimc.launcher                  I  Breakpad config: directory is: /data/user/0/org.levimc.launcher/crash, sessionid is: 94c1f454-f53c-4716-b9b4-dd6d198a7476
2025-07-29 18:35:46.981 27460-27571 libc                    org.levimc.launcher                  W  Access denied finding property "ro.mediatek.platform"
2025-07-29 18:35:46.989 27460-27571 System.out              org.levimc.launcher                  I  getwidth: 2340
2025-07-29 18:35:46.990 27460-27571 System.out              org.levimc.launcher                  I  getheight: 1080
2025-07-29 18:35:46.995 27460-27571 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using AppData dir - CurrentFileStoragePath is now '/data/user/0/org.levimc.launcher'
2025-07-29 18:35:47.052 27460-27460 AppExitInfoHelper       org.levimc.launcher                  I  Registering session ID for ApplicationExitInfo: 94c1f454-f53c-4716-b9b4-dd6d198a7476
2025-07-29 18:35:47.067 27460-27571 SwappyDisplayManager    org.levimc.launcher                  I  Using internal com/google/androidgamesdk/SwappyDisplayManager class from dex bytes.
2025-07-29 18:35:47.070 27460-27571 SwappyDisplayManager    org.levimc.launcher                  E  dalvik.system.InMemoryDexClassLoader[DexPathList[[dex file "InMemoryDexFile[cookie=[0, -5476376622451398048]]"],nativeLibraryDirectories=[/system/lib64, /system_ext/lib64]]] couldn't find "libpreloader.so"
2025-07-29 18:35:47.081 27460-27576 SwappyDisplayManager    org.levimc.launcher                  I  Starting looper thread
2025-07-29 18:35:47.083 27460-27571 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb400007a1a8dbd40
2025-07-29 18:35:47.083 27460-27571 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-29 18:35:47.085 27460-27460 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze.configure() called with configuration: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-29 18:35:47.086 27460-27460 MinecraftPlatform       org.levimc.launcher                  I  MainActivity::requestPushPermission
2025-07-29 18:35:47.095 27460-27460 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:31)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:35:47.102 27460-27460 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:32)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:35:47.107 27460-27460 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:33)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:35:47.112 27460-27460 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:34)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:35:47.116 27460-27460 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.mojang.minecraftpe.NetworkMonitor._addNetworkCallbacksForTransport(NetworkMonitor.java:45)] [com.mojang.minecraftpe.NetworkMonitor.<init>(NetworkMonitor.java:37)] [com.mojang.minecraftpe.MainActivity.onCreate(MainActivity.java:411)] [com.mojang.minecraftpe.Launcher.onCreate(Launcher.java:22)] [android.app.Activity.performCreate(Activity.java:9363)] [android.app.Activity.performCreate(Activity.java:9332)] [android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1526)] [android.app.ActivityThread.performLaunchActivity(ActivityThread.java:4440)] [android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:4689)] [android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:112)] [android.app.servertransaction.TransactionExecutor.executeNonLifecycleItem(TransactionExecutor.java:174)] [android.app.servertransaction.TransactionExecutor.executeTransactionItems(TransactionExecutor.java:109)] [android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:81)] [android.app.ActivityThread$H.handleMessage(ActivityThread.java:2899)]
2025-07-29 18:35:47.118 27460-27571 AppExitInfoHelper       org.levimc.launcher                  I  Received session ID from ApplicationExitInfo: 
2025-07-29 18:35:47.138 27460-27460 InputMethodManager      org.levimc.launcher                  I  invalidateInput
2025-07-29 18:35:47.138 27460-27460 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 303326708; UID 10637; state: ENABLED
2025-07-29 18:35:47.141 27460-27460 MinecraftPE             org.levimc.launcher                  D  onStart
2025-07-29 18:35:47.181 27460-27571 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was gained
2025-07-29 18:35:47.187 27460-27460 Minecraft               org.levimc.launcher                  W  INPUT device id -1- is Crete Controller: false
2025-07-29 18:35:47.187 27460-27460 Minecraft               org.levimc.launcher                  W  INPUT device id 2- is Crete Controller: false
2025-07-29 18:35:47.187 27460-27460 Minecraft               org.levimc.launcher                  W  INPUT device id 3- is Crete Controller: false
2025-07-29 18:35:47.188 27460-27460 Minecraft               org.levimc.launcher                  W  INPUT device id 4- is Crete Controller: false
2025-07-29 18:35:47.188 27460-27460 Minecraft               org.levimc.launcher                  W  INPUT device id 5- is Crete Controller: false
2025-07-29 18:35:47.188 27460-27460 Minecraft               org.levimc.launcher                  W  INPUT device id 6- is Crete Controller: false
2025-07-29 18:35:47.188 27460-27460 Minecraft               org.levimc.launcher                  W  No Xbox Controller Found
2025-07-29 18:35:47.188 27460-27460 Minecraft               org.levimc.launcher                  W  No Playstation Controller Found
2025-07-29 18:35:47.188 27460-27460 Minecraft               org.levimc.launcher                  W  No PS Dualsense Controller Found
2025-07-29 18:35:47.189 27460-27460 MinecraftPE             org.levimc.launcher                  D  onResume
2025-07-29 18:35:47.207 27460-27460 Braze v24....ageManager org.levimc.launcher                  V  Registering InAppMessageManager with activity: com.mojang.minecraftpe.Launcher
2025-07-29 18:35:47.212 27460-27460 Braze v24....ageManager org.levimc.launcher                  D  Subscribing in-app message event subscriber
2025-07-29 18:35:47.212 27460-27460 Braze v24.3.0 .Braze    org.levimc.launcher                  V  The instance is null. Allowing instance initialization
2025-07-29 18:35:47.212 27460-27460 Braze v24.3.0 .Braze    org.levimc.launcher                  V  The instance is null. Allowing instance initialization
2025-07-29 18:35:47.212 27460-27460 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze SDK Initializing
2025-07-29 18:35:47.239 27460-27589 Braze v24....mageLoader org.levimc.launcher                  D  Initializing disk cache
2025-07-29 18:35:47.244 27460-27460 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Braze SDK loaded in 31 ms.
2025-07-29 18:35:47.244 27460-27460 Braze v24....ageManager org.levimc.launcher                  V  Subscribing sdk data wipe subscriber
2025-07-29 18:35:47.245 27460-27592 Braze v24.3.0 .Braze    org.levimc.launcher                  D  Applying any pending runtime configuration values
2025-07-29 18:35:47.245 27460-27592 Braze v24.3.0 .Braze    org.levimc.launcher                  V  Setting pending config object: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-29 18:35:47.245 27460-27592 Braze v24....onProvider org.levimc.launcher                  I  Setting Braze Override configuration with config: Builder(apiKey=7e90f2bd-d27b-4010-a501-a8e30021418a, serverTarget=null, smallNotificationIconName=drawable/notification_icon_small, largeNotificationIconName=null, customEndpoint=null, defaultNotificationChannelName=null, defaultNotificationChannelDescription=null, pushDeepLinkBackStackActivityClassName=null, firebaseCloudMessagingSenderIdKey=null, customHtmlWebViewActivityClassName=null, sdkFlavor=null, sessionTimeout=10, defaultNotificationAccentColor=5415989, triggerActionMinimumTimeIntervalSeconds=5, badNetworkInterval=120, goodNetworkInterval=60, greatNetworkInterval=10, inAppMessageWebViewClientMaxOnPageFinishedWaitMs=null, admMessagingRegistrationEnabled=false, handlePushDeepLinksAutomatically=true, isLocationCollectionEnabled=false, isNewsFeedVisualIndicatorOn=false, isPushDeepLinkBackStackActivityEnabled=null, isSessionStartBasedTimeoutEnabled=null, isFirebaseCloudMessagingRegistrationEnabled=false, isContentCardsUnreadVisualIndicatorEnabled=null, isInAppMessageAccessibilityExclusiveModeEnabled=null, isPushWakeScreenForNotificationEnabled=false, isPushHtmlRenderingEnabled=null, isGeofencesEnabled=false, inAppMessageTestPushEagerDisplayEnabled=null, automaticGeofenceRequestsEnabled=null, isFirebaseMessagingServiceOnNewTokenRegistrationEnabled=false, isTouchModeRequiredForHtmlInAppMessages=null, isSdkAuthEnabled=null, deviceObjectAllowlist=null, isDeviceObjectAllowlistEnabled=null, brazeSdkMetadata=null, customLocationProviderNames=null, isHtmlInAppMessageApplyWindowInsetsEnabled=null, doesPushStoryDismissOnClick=null)
2025-07-29 18:35:47.245 27460-27589 Braze v24....mageLoader org.levimc.launcher                  D  Disk cache initialized
2025-07-29 18:35:47.246 27460-27592 Braze v24....onProvider org.levimc.launcher                  I  Found an override api key. Using it to configure the Braze SDK
2025-07-29 18:35:47.247 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml INTEGER configuration value with primary key 'com_braze_logger_initial_log_level'. Using default value '4'.
2025-07-29 18:35:47.247 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_logger_initial_log_level' and value: '4'
2025-07-29 18:35:47.259 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_custom_endpoint'. Using default value 'null'.
2025-07-29 18:35:47.260 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_custom_endpoint' and value: 'null'
2025-07-29 18:35:47.260 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_firebase_cloud_messaging_registration_enabled' and value: 'false'
2025-07-29 18:35:47.260 27460-27592 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Automatic Firebase Cloud Messaging registration not enabled in configuration. Braze will not register for Firebase Cloud Messaging.
2025-07-29 18:35:47.260 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_push_adm_messaging_registration_enabled' and value: 'false'
2025-07-29 18:35:47.260 27460-27592 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Automatic ADM registration not enabled in configuration. Braze will not register for ADM.
2025-07-29 18:35:47.262 27460-27592 Braze v24.3.0 .Braze    org.levimc.launcher                  V  Starting up a new user dependency manager
2025-07-29 18:35:47.281 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_session_timeout' and value: '10'
2025-07-29 18:35:47.282 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_session_start_based_timeout_enabled'. Using default value 'false'.
2025-07-29 18:35:47.282 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_session_start_based_timeout_enabled' and value: 'false'
2025-07-29 18:35:47.288 27460-27460 ViewRootImpl            org.levimc.launcher                  I  dVRR is disabled
2025-07-29 18:35:47.289 27460-27493 NativeCust...ncyManager org.levimc.launcher                  D  [NativeCFMS] BpCustomFrequencyManager::BpCustomFrequencyManager()
2025-07-29 18:35:47.291 27460-27460 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=statusBars captionBar, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.setView:1955 android.view.WindowManagerGlobal.addView:578 android.view.WindowManagerImpl.addView:158 android.app.ActivityThread.handleResumeActivity:6064 
2025-07-29 18:35:47.296 27460-27460 InputTransport          org.levimc.launcher                  D  Input channel constructed: '171d684', fd=207
2025-07-29 18:35:47.297 27460-27460 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.setView:1999, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:35:47.297 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  synced displayState. AttachInfo displayState=2
2025-07-29 18:35:47.298 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  setView = com.android.internal.policy.DecorView@ebae05e IsHRR=false TM=true
2025-07-29 18:35:47.298 27460-27460 ImeFocusController      org.levimc.launcher                  I  onPreWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-29 18:35:47.298 27460-27460 ImeFocusController      org.levimc.launcher                  I  onPostWindowFocus: skipped hasWindowFocus=false mHasImeFocus=true
2025-07-29 18:35:47.299 27460-27460 SurfaceView@6502654     org.levimc.launcher                  I  onWindowVisibilityChanged(0) true com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{6502654 V.E...... ......I. 0,0-0,0} of VRI[Launcher]@80d8512
2025-07-29 18:35:47.300 27460-27460 SurfaceView             org.levimc.launcher                  D  105915988 updateSurface: has no frame
2025-07-29 18:35:47.302 27460-27460 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:35:47.302 27460-27460 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:35:47.308 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_trigger_action_minimum_time_interval_seconds' and value: '5'
2025-07-29 18:35:47.315 27460-27592 Braze v24.....bo.app.d6 org.levimc.launcher                  V  Subscribing to trigger dispatch events.
2025-07-29 18:35:47.322 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_custom_location_providers_list'. Using default value '[]'.
2025-07-29 18:35:47.322 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_custom_location_providers_list' and value: '[]'
2025-07-29 18:35:47.324 27460-27592 Braze v24....nceManager org.levimc.launcher                  D  Did not find stored geofences.
2025-07-29 18:35:47.329 27460-27592 Braze v24....nceManager org.levimc.launcher                  I  Geofences implicitly disabled via server configuration.
2025-07-29 18:35:47.329 27460-27592 Braze v24....nceManager org.levimc.launcher                  I  ***Geofence API not found. Please include the android-sdk-location module***
2025-07-29 18:35:47.329 27460-27592 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Geofences not set up.
2025-07-29 18:35:47.329 27460-27592 Braze v24.3.0 .o        org.levimc.launcher                  I  ***Location API not found. Please include android-sdk-location module***
2025-07-29 18:35:47.332 27460-27592 Braze v24.3.0 .f1       org.levimc.launcher                  D  Did not find stored feature flags.
2025-07-29 18:35:47.336 27460-27460 BufferQueueConsumer     org.levimc.launcher                  D  [](id:6b4400000003,api:0,p:-1,c:27460) connect: controlledByApp=false
2025-07-29 18:35:47.338 27460-27460 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@80d8512#3](f:0,a:0,s:0) constructor()
2025-07-29 18:35:47.339 27460-27460 BLASTBufferQueue_Java   org.levimc.launcher                  I  new BLASTBufferQueue, mName= VRI[Launcher]@80d8512 mNativeObject= 0xb4000078ea2adc00 sc.mNativeObject= 0xb4000078e9ef4ac0 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 android.view.Choreographer.doCallbacks:1216 android.view.Choreographer.doFrame:1142 android.view.Choreographer$FrameDisplayEventReceiver.run:1707 
2025-07-29 18:35:47.339 27460-27460 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@80d8512 mNativeObject= 0xb4000078ea2adc00 sc.mNativeObject= 0xb4000078e9ef4ac0 format= -3 caller= android.graphics.BLASTBufferQueue.<init>:88 android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3397 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 
2025-07-29 18:35:47.339 27460-27460 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@80d8512#3](f:0,a:0,s:0) update width=2340 height=1080 format=-3 mTransformHint=4
2025-07-29 18:35:47.340 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)0 dur=32 res=0x3 s={true 0xb4000078ea4c1000} ch=true seqId=0
2025-07-29 18:35:47.345 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  performConfigurationChange setNightDimText nightDimLevel=0
2025-07-29 18:35:47.345 27460-27460 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:35:47.345 27460-27460 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:35:47.346 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  mThreadedRenderer.initialize() mSurface={isValid=true 0xb4000078ea4c1000} hwInitialized=true
2025-07-29 18:35:47.346 27460-27460 SurfaceView             org.levimc.launcher                  D  105915988 updateSurface: has no frame
2025-07-29 18:35:47.347 27460-27460 SurfaceView@6502654     org.levimc.launcher                  I  windowStopped(false) true com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{6502654 V.E...... ......ID 0,0-2340,1080} of VRI[Launcher]@80d8512
2025-07-29 18:35:47.347 27460-27460 SurfaceView             org.levimc.launcher                  D  105915988 updateSurface: has no frame
2025-07-29 18:35:47.347 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.performTraversals:5193 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 android.view.Choreographer$CallbackRecord.run:1760 
2025-07-29 18:35:47.347 27460-27460 SurfaceView             org.levimc.launcher                  I  105915988 Changes: creating=true format=true size=true visible=true alpha=false hint=true visible=true left=true top=true z=false attached=true lifecycleStrategy=false
2025-07-29 18:35:47.353 27460-27460 BufferQueueConsumer     org.levimc.launcher                  D  [](id:6b4400000004,api:0,p:-1,c:27460) connect: controlledByApp=false
2025-07-29 18:35:47.355 27460-27460 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) constructor()
2025-07-29 18:35:47.356 27460-27460 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = null mNativeObject= 0xb4000078ea463000 sc.mNativeObject= 0xb4000078ea220200 format= 4 caller= android.view.SurfaceView.createBlastSurfaceControls:1642 android.view.SurfaceView.updateSurface:1318 android.view.SurfaceView.lambda$new$0:268 android.view.SurfaceView.$r8$lambda$NfZyM_TG8F8lqzaOVZ7noREFjzU:0 android.view.SurfaceView$$ExternalSyntheticLambda1.onPreDraw:0 android.view.ViewTreeObserver.dispatchOnPreDraw:1226 
2025-07-29 18:35:47.356 27460-27460 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) update width=2340 height=1080 format=4 mTransformHint=4
2025-07-29 18:35:47.357 27460-27460 SurfaceView             org.levimc.launcher                  I  105915988 Cur surface: Surface(name=null mNativeObject=0)/@0x980f70d
2025-07-29 18:35:47.357 27460-27460 SurfaceView@6502654     org.levimc.launcher                  I  pST: sr = Rect(0, 0 - 2340, 1080) sw = 2340 sh = 1080
2025-07-29 18:35:47.357 27460-27460 SurfaceView             org.levimc.launcher                  D  105915988 performSurfaceTransaction RenderWorker position = [0, 0, 2340, 1080] surfaceSize = 2340x1080
2025-07-29 18:35:47.358 27460-27460 SurfaceView@6502654     org.levimc.launcher                  I  updateSurface: mVisible = true mSurface.isValid() = true
2025-07-29 18:35:47.358 27460-27460 SurfaceView@6502654     org.levimc.launcher                  I  updateSurface: mSurfaceCreated = false surfaceChanged = true visibleChanged = true
2025-07-29 18:35:47.358 27460-27460 SurfaceView             org.levimc.launcher                  I  105915988 visibleChanged -- surfaceCreated
2025-07-29 18:35:47.358 27460-27460 SurfaceView@6502654     org.levimc.launcher                  I  surfaceCreated 1 #8 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{6502654 V.E...... ......ID 0,0-2340,1080}
2025-07-29 18:35:47.365 27460-27592 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: NONE
2025-07-29 18:35:47.365 27460-27592 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:35:47.365 27460-27592 Braze v24.3.0 .i0       org.levimc.launcher                  D  Data sync started
2025-07-29 18:35:47.365 27460-27592 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.registerDefaultNetworkCallbackForUid(ConnectivityManager.java:5461)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5428)] [android.net.ConnectivityManager.registerDefaultNetworkCallback(ConnectivityManager.java:5402)] [bo.app.i0.d(SourceFile:3)] [bo.app.i0.e(SourceFile:6)] [bo.app.i0.a(SourceFile:10)] [bo.app.n6.<init>(SourceFile:227)] [com.braze.Braze$d.a(SourceFile:63)] [com.braze.Braze$d.invoke(SourceFile:1)] [com.braze.Braze$r2$a.invokeSuspend(SourceFile:2)] [kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)] [kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)] [kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:284)] [kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:85)] [kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:59)] [kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source:1)] [kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking$default(Builders.kt:38)] [kotlinx.coroutines.BuildersKt.runBlocking$default(Unknown Source:1)] [com.braze.Braze$r2.invokeSuspend(SourceFile:2)] [kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)] [kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)] [java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1156)] [java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:651)] [java.lang.Thread.run(Thread.java:1119)]
2025-07-29 18:35:47.370 27460-27592 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-29 18:35:47.370 27460-27592 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:35:47.374 27460-27579 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-29 18:35:47.374 27460-27579 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:35:47.380 27460-27592 Braze v24.3.0 .c1       org.levimc.launcher                  D  Started offline event recovery task.
2025-07-29 18:35:47.382 27460-27571 Minecraft               org.levimc.launcher                  W  NO LOG FILE! - [Graphics] The graphics context was gained
2025-07-29 18:35:47.382 27460-27460 SurfaceView             org.levimc.launcher                  I  105915988 surfaceChanged -- format=4 w=2340 h=1080
2025-07-29 18:35:47.383 27460-27571 Minecraft               org.levimc.launcher                  W  MinecraftGame::init && MinecraftGame::setSize!
2025-07-29 18:35:47.383 27460-27460 SurfaceView@6502654     org.levimc.launcher                  I  surfaceChanged (2340,1080) 1 #8 com.google.androidgamesdk.GameActivity$InputEnabledSurfaceView{6502654 V.E...... ......ID 0,0-2340,1080}
2025-07-29 18:35:47.383 27460-27460 SurfaceView             org.levimc.launcher                  I  105915988 surfaceRedrawNeeded
2025-07-29 18:35:47.384 27460-27460 SurfaceView             org.levimc.launcher                  I  105915988 finishedDrawing
2025-07-29 18:35:47.384 27460-27460 SurfaceView             org.levimc.launcher                  V  Layout: x=0 y=0 w=2340 h=1080, frame=Rect(0, 0 - 2340, 1080)
2025-07-29 18:35:47.385 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@80d8512#9
2025-07-29 18:35:47.385 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@80d8512#10
2025-07-29 18:35:47.387 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-29 18:35:47.388 27460-27493 SurfaceView             org.levimc.launcher                  D  105915988 updateSurfacePosition RenderWorker, frameNr = 1, position = [0, 0, 2340, 1080] surfaceSize = 2340x1080
2025-07-29 18:35:47.388 27460-27493 SurfaceView@6502654     org.levimc.launcher                  I  uSP: rtp = Rect(0, 0 - 2340, 1080) rtsw = 2340 rtsh = 1080
2025-07-29 18:35:47.388 27460-27493 SurfaceView@6502654     org.levimc.launcher                  I  onSSPAndSRT: pl = 0 pt = 0 sx = 1.0 sy = 1.0
2025-07-29 18:35:47.389 27460-27493 SurfaceView@6502654     org.levimc.launcher                  I  aOrMT: VRI[Launcher]@80d8512 t = android.view.SurfaceControl$Transaction@7357a17 fN = 1 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1792 android.graphics.RenderNode$CompositePositionUpdateListener.positionChanged:398 
2025-07-29 18:35:47.389 27460-27493 VRI[Launcher]@80d8512   org.levimc.launcher                  I  mWNT: t=0xb4000078e9fda400 mBlastBufferQueue=0xb4000078ea2adc00 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.SurfaceView.applyOrMergeTransaction:1723 android.view.SurfaceView.-$$Nest$mapplyOrMergeTransaction:0 android.view.SurfaceView$SurfaceViewPositionUpdateListener.positionChanged:1792 
2025-07-29 18:35:47.390 27460-27520 VRI[Launcher]@80d8512   org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=1.
2025-07-29 18:35:47.391 27460-27520 VRI[Launcher]@80d8512   org.levimc.launcher                  I  mWNT: t=0xb4000078e9fda880 mBlastBufferQueue=0xb4000078ea2adc00 fn= 1 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$11.onFrameDraw:15016 android.view.ThreadedRenderer$1.onFrameDraw:761 <bottom of call stack> 
2025-07-29 18:35:47.392 27460-27520 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-29 18:35:47.392 27460-27592 Braze v24.3.0 .c1       org.levimc.launcher                  V  Adding event to dispatch from storage: {"name":"ss","data":{},"time":1.753792501792E9,"user_id":"7115A474BB854844","session_id":"7938609f-cb95-49a8-b324-d9d567c4cd27"}
2025-07-29 18:35:47.393 27460-27585 libMEOW                 org.levimc.launcher                  D  meow new tls: 0xb400007a0a804040
2025-07-29 18:35:47.393 27460-27585 libMEOW                 org.levimc.launcher                  D  applied 0 plugin for [org.levimc.launcher].
2025-07-29 18:35:47.394 27460-27592 Braze v24.3.0 .c1       org.levimc.launcher                  V  Adding event to dispatch from storage: {"name":"se","data":{"d":23},"time":1.753792501788E9,"user_id":"7115A474BB854844","session_id":"05fe2783-0bfe-481d-966d-6ceba0a5ceba"}
2025-07-29 18:35:47.396 27460-27493 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@80d8512#3](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-29 18:35:47.397 27460-27493 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[Launcher]@80d8512#3](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=272488573358010(auto) mPendingTransactions.size=0 graphicBufferId=117939801948196 transform=7
2025-07-29 18:35:47.398 27460-27493 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true
2025-07-29 18:35:47.399 27460-27493 HWUI                    org.levimc.launcher                  D  CFMS:: SetUp Pid : 27460    Tid : 27493
2025-07-29 18:35:47.399 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-29 18:35:47.409 27460-27493 HWUI                    org.levimc.launcher                  D  HWUI - treat SMPTE_170M as sRGB
2025-07-29 18:35:47.410 27460-27460 InsetsController        org.levimc.launcher                  I  setRequestedVisibleTypes: visible=false, mask=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.InsetsController.controlAnimationUnchecked:1498 android.view.InsetsController.applyAnimation:2228 android.view.InsetsController.applyAnimation:2159 android.view.InsetsController.hide:1452 android.view.InsetsController.hide:1368 android.view.ViewRootImpl.controlInsetsForCompatibility:3953 android.view.ViewRootImpl.performTraversals:4497 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:35:47.411 27460-27460 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@80d8512 mNativeObject= 0xb4000078ea2adc00 sc.mNativeObject= 0xb4000078e9ef4ac0 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:35:47.411 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=0 res=0x0 s={true 0xb4000078ea4c1000} ch=false seqId=0
2025-07-29 18:35:47.412 27460-27460 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:35:47.413 27460-27460 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:35:47.414 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  updateBoundsLayer: t=android.view.SurfaceControl$Transaction@2b75e22 sc=Surface(name=Bounds for - org.levimc.launcher/com.mojang.minecraftpe.Launcher@0)/@0x70da0b3 frame=2
2025-07-29 18:35:47.414 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-29 18:35:47.415 27460-27519 VRI[Launcher]@80d8512   org.levimc.launcher                  I  mWNT: t=0xb4000078e9b5b900 mBlastBufferQueue=0xb4000078ea2adc00 fn= 2 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-29 18:35:47.423 27460-27460 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleInsetsControlChanged:2888, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=false mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:35:47.424 27460-27460 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=navigationBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-29 18:35:47.425 27460-27460 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=statusBars, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-29 18:35:47.426 27460-27460 InsetsController        org.levimc.launcher                  I  onStateChanged: host=org.levimc.launcher/com.mojang.minecraftpe.Launcher, from=android.view.ViewRootImpl.handleResized:2789, state=InsetsState: {mDisplayFrame=Rect(0, 0 - 2340, 1080), mDisplayCutout=DisplayCutout{insets=Rect(96, 0 - 0, 0) waterfall=Insets{left=0, top=0, right=0, bottom=0} boundingRect={Bounds=[Rect(0, 472 - 96, 608), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0), Rect(0, 0 - 0, 0)]} cutoutPathParserInfo={CutoutPathParserInfo{displayWidth=1080 displayHeight=2340 physicalDisplayWidth=1080 physicalDisplayHeight=2340 density={2.8125} cutoutSpec={M 0,0 H -24.177777778 V 34.13333333333333 H 24.177777778 V 0 H 0 Z @dp} rotation={1} scale={1.0} physicalPixelDisplaySizeRatio={1.0}}} sideOverrides={}}, mRoundedCorners=RoundedCorners{[RoundedCorner{position=TopLeft, radius=101, center=Point(101, 101)}, RoundedCorner{position=TopRight, radius=101, center=Point(2239, 101)}, RoundedCorner{position=BottomRight, radius=101, center=Point(2239, 979)}, RoundedCorner{position=BottomLeft, radius=101, center=Point(101, 979)}]}  mRoundedCornerFrame=Rect(0, 0 - 2340, 1080), mPrivacyIndicatorBounds=PrivacyIndicatorBounds {static bounds=Rect(2216, 0 - 2340, 73) rotation=1}, mDisplayShape=DisplayShape{ spec=-311912193 displayWidth=1080 displayHeight=2340 physicalPixelDisplaySizeRatio=1.0 rotation=1 offsetX=0 offsetY=0 scale=1.0}, mSources= { InsetsSource: {de860000 mType=statusBars mFrame=[0,0][2340,73] mVisible=false mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860005 mType=mandatorySystemGestures mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {de860006 mType=tappableElement mFrame=[0,0][2340,73] mVisible=true mFlags= mSideHint=TOP mBoundingRects=null}, InsetsSource: {3 mType=ime mFrame=[0,0][0,0] mVisible=false mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {7 mType=displayCutout mFrame=[0,0][96,1080] mVisible=true mFlags= mSideHint=LEFT mBoundingRects=null}, InsetsSource: {21160001 mType=navigationBars mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160004 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null}, InsetsSource: {21160005 mType=mandatorySystemGestures mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160006 mType=tappableElement mFrame=[2205,0][2340,1080] mVisible=true mFlags= mSideHint=RIGHT mBoundingRects=null}, InsetsSource: {21160024 mType=systemGestures mFrame=[0,0][0,0] mVisible=true mFlags= mSideHint=NONE mBoundingRects=null} }
2025-07-29 18:35:47.426 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:35:47.426 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  handleResized mSyncSeqId = 0
2025-07-29 18:35:47.426 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  reportNextDraw android.view.ViewRootImpl.handleResized:2864 android.view.ViewRootImpl.-$$Nest$mhandleResized:0 android.view.ViewRootImpl$W.resized:13691 android.app.servertransaction.WindowStateResizeItem.execute:64 android.app.servertransaction.WindowStateTransactionItem.execute:59 
2025-07-29 18:35:47.426 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-29 18:35:47.426 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-29 18:35:47.426 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  handleAppVisibility mAppVisible = true visible = false
2025-07-29 18:35:47.426 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  visibilityChanged oldVisibility=true newVisibility=false
2025-07-29 18:35:47.427 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  handleResized, frames=ClientWindowFrames{frame=[0,0][2340,1080] display=[0,0][2340,1080] parentFrame=[0,0][0,0]} displayId=0 dragResizing=false compatScale=1.0 frameChanged=false attachedFrameChanged=false configChanged=false displayChanged=false compatScaleChanged=false dragResizingChanged=false
2025-07-29 18:35:47.427 27460-27460 gti.InputConnection     org.levimc.launcher                  D  onApplyWindowInsetsfalse
2025-07-29 18:35:47.427 27460-27460 GameActivity            org.levimc.launcher                  V  onImeInsetsChanged from Text Listener
2025-07-29 18:35:47.429 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  Setup new sync=wmsSync-VRI[Launcher]@80d8512#11
2025-07-29 18:35:47.429 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Creating new active sync group VRI[Launcher]@80d8512#12
2025-07-29 18:35:47.429 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  registerCallbacksForSync syncBuffer=false
2025-07-29 18:35:47.431 27460-27520 VRI[Launcher]@80d8512   org.levimc.launcher                  D  Received frameDrawingCallback syncResult=0 frameNum=3.
2025-07-29 18:35:47.431 27460-27520 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Setting up sync and frameCommitCallback
2025-07-29 18:35:47.437 27460-27493 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=true
2025-07-29 18:35:47.437 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  reportDrawFinished seqId=0
2025-07-29 18:35:47.448 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  Relayout returned: old=(982,335,1358,745) new=(982,335,1358,745) relayoutAsync=false req=(376,410)8 dur=7 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-29 18:35:47.448 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-29 18:35:47.458 27460-27516 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@36f01e2#2](f:0,a:3,s:0) destructor()
2025-07-29 18:35:47.459 27460-27516 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@36f01e2#2(BLAST Consumer)2](id:6b4400000002,api:0,p:-1,c:27460) disconnect
2025-07-29 18:35:47.459 27460-27460 BLASTBufferQueue        org.levimc.launcher                  I  [VRI[MainActivity]@2f3909d#1](f:0,a:3,s:0) destructor()
2025-07-29 18:35:47.459 27460-27460 BufferQueueConsumer     org.levimc.launcher                  D  [VRI[MainActivity]@2f3909d#1(BLAST Consumer)1](id:6b4400000001,api:0,p:-1,c:27460) disconnect
2025-07-29 18:35:47.460 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=false req=(2340,1080)8 dur=8 res=0x2 s={false 0x0} ch=true seqId=0
2025-07-29 18:35:47.460 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  Not drawing due to not visible. Reason=!mAppVisible && !mForceDecorViewVisibility
2025-07-29 18:35:47.461 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  D  mThreadedRenderer.initializeIfNeeded()#2 mSurface={isValid=true 0xb4000078ea4c1000}
2025-07-29 18:35:47.462 27460-27460 InputMethodManagerUtils org.levimc.launcher                  D  startInputInner - Id : 0
2025-07-29 18:35:47.462 27460-27460 InputMethodManager      org.levimc.launcher                  I  startInputInner - IInputMethodManagerGlobalInvoker.startInputOrWindowGainedFocus
2025-07-29 18:35:47.466 27460-27516 InputTransport          org.levimc.launcher                  D  Input channel constructed: 'ClientS', fd=150
2025-07-29 18:35:47.477 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  stopped(true) old = false
2025-07-29 18:35:47.477 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-29 18:35:47.479 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  stopped(true) old = false
2025-07-29 18:35:47.479 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  D  WindowStopped on org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity set to true
2025-07-29 18:35:47.483 27460-27592 Braze v24.3.0 .q        org.levimc.launcher                  D  Messaging session stopped. Adding new messaging session timestamp: 1753792547
2025-07-29 18:35:47.483 27460-27460 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@4053655
2025-07-29 18:35:47.484 27460-27592 Braze v24.3.0 .p        org.levimc.launcher                  V  Closed session with activity: ui.activities.MainActivity
2025-07-29 18:35:47.484 27460-27592 Braze v24.3.0 .u        org.levimc.launcher                  D  Getting the stored open session
2025-07-29 18:35:47.485 27460-27592 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 7938609f-cb95-49a8-b324-d9d567c4cd27
2025-07-29 18:35:47.485 27460-27460 VRI[MainAc...y]@2f3909d org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-29 18:35:47.487 27460-27460 InputTransport          org.levimc.launcher                  D  Input channel destroyed: '3df0d9a', fd=211
2025-07-29 18:35:47.488 27460-27592 Braze v24.3.0 .u        org.levimc.launcher                  I  Session [7938609f-cb95-49a8-b324-d9d567c4cd27] being sealed because its end time is over the grace period. Session: 
                                                                                                    MutableSession(sessionId=7938609f-cb95-49a8-b324-d9d567c4cd27, startTime=1.753792501791E9, endTime=1.753792501799E9, isSealed=false, duration=0)
2025-07-29 18:35:47.489 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.f5 fired: SessionSealedEvent(sealedSession=
                                                                                                    MutableSession(sessionId=7938609f-cb95-49a8-b324-d9d567c4cd27, startTime=1.753792501791E9, endTime=1.753792547488E9, isSealed=true, duration=45))
2025-07-29 18:35:47.489 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.f5 on 1 subscribers.
2025-07-29 18:35:47.491 27460-27592 Braze v24.3.0 .p        org.levimc.launcher                  D  Not adding session id to event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 45
                                                                                                      },
                                                                                                      "time": 1.753792547489E9,
                                                                                                      "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                    }
2025-07-29 18:35:47.491 27460-27460 WindowManager           org.levimc.launcher                  E  android.view.WindowLeaked: Activity org.levimc.launcher.ui.activities.MainActivity has leaked window com.android.internal.policy.DecorView{64fdaad V.ED..... R.....ID 0,0-376,410 aid=1073741828}[MainActivity] that was originally added here
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1527)
                                                                                                    	at android.view.ViewRootImpl.<init>(ViewRootImpl.java:1502)
                                                                                                    	at android.view.WindowManagerGlobal.addView(WindowManagerGlobal.java:544)
                                                                                                    	at android.view.WindowManagerImpl.addView(WindowManagerImpl.java:158)
                                                                                                    	at android.app.Dialog.show(Dialog.java:511)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher.showLoading(MinecraftLauncher.java:308)
                                                                                                    	at org.levimc.launcher.core.minecraft.MinecraftLauncher$$ExternalSyntheticLambda4.run(D8$$SyntheticClass:0)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
2025-07-29 18:35:47.491 27460-27592 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "se",
                                                                                                      "data": {
                                                                                                        "d": 45
                                                                                                      },
                                                                                                      "time": 1.753792547489E9,
                                                                                                      "user_id": "7115A474BB854844",
                                                                                                      "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                    }
2025-07-29 18:35:47.492 27460-27460 WindowManager           org.levimc.launcher                  I  WindowManagerGlobal#removeView, ty=2, view=com.android.internal.policy.DecorView{64fdaad V.ED..... R.....ID 0,0-376,410 aid=1073741828}[MainActivity], caller=android.view.WindowManagerGlobal.closeAllExceptView:672 android.view.WindowManagerGlobal.closeAll:644 android.app.ActivityThread.handleDestroyActivity:6747 
2025-07-29 18:35:47.493 27460-27591 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid 4fb1e208-703b-453b-8a40-ec9fbc7d52f3
2025-07-29 18:35:47.496 27460-27460 ConnectivityManager     org.levimc.launcher                  D  StackLog: [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4736)] [android.net.ConnectivityManager.sendRequestForNetwork(ConnectivityManager.java:4904)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5321)] [android.net.ConnectivityManager.registerNetworkCallback(ConnectivityManager.java:5291)] [com.xbox.httpclient.NetworkObserver.Initialize(NetworkObserver.java:72)] [com.mojang.minecraftpe.MainActivity.nativeRunNativeCallbackOnUiThread(Native Method)] [com.mojang.minecraftpe.MainActivity$19.call(MainActivity.java:2136)] [com.mojang.minecraftpe.MainActivity$19.call(MainActivity.java:2133)] [java.util.concurrent.FutureTask.run(FutureTask.java:317)]
2025-07-29 18:35:47.502 27460-27460 InsetsSourceConsumer    org.levimc.launcher                  I  applyRequestedVisibilityToControl: visible=false, type=ime, host=org.levimc.launcher/com.mojang.minecraftpe.Launcher
2025-07-29 18:35:47.504  1598-1804  WindowManager           system_server                        E  win=Window{7125056 u0 org.levimc.launcher/org.levimc.launcher.ui.activities.MainActivity EXITING} destroySurfaces: appStopped=false cleanupOnResume=false win.mWindowRemovalAllowed=true win.mRemoveOnExit=true win.mViewVisibility=8 caller=com.android.server.wm.WindowState.onExitAnimationDone:222 com.android.server.wm.WindowState.onAnimationFinished:161 com.android.server.wm.WindowContainer$$ExternalSyntheticLambda5.onAnimationFinished:26 com.android.server.wm.SurfaceAnimator.cancelAnimation:19 com.android.server.wm.SurfaceAnimator.cancelAnimation:1 com.android.server.wm.WindowContainer.setParent:49 com.android.server.wm.WindowContainer.removeChild:17 
2025-07-29 18:35:47.504 27460-27460 WindowOnBackDispatcher  org.levimc.launcher                  W  sendCancelIfRunning: isInProgress=false callback=android.view.ViewRootImpl$$ExternalSyntheticLambda15@53c74a9
2025-07-29 18:35:47.505 27460-27460 VRI[MainAc...y]@36f01e2 org.levimc.launcher                  I  dispatchDetachedFromWindow
2025-07-29 18:35:47.509 27460-27460 InputTransport          org.levimc.launcher                  D  Input channel destroyed: '7125056', fd=149
2025-07-29 18:35:47.511 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"se","data":{"d":45},"time":1.753792547489E9,"user_id":"7115A474BB854844","session_id":"7938609f-cb95-49a8-b324-d9d567c4cd27"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-29 18:35:47.511 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:35:47.513 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='7938609f-cb95-49a8-b324-d9d567c4cd27', eventType='SESSION_ENDED'}'
2025-07-29 18:35:47.514 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-29 18:35:47.514 27460-27592 Braze v24.3.0 .u        org.levimc.launcher                  I  New session created with ID: 4f989e4b-1d77-4b12-a767-fe2f534213c5
2025-07-29 18:35:47.514 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.d5 fired: SessionCreatedEvent(session=
                                                                                                    MutableSession(sessionId=4f989e4b-1d77-4b12-a767-fe2f534213c5, startTime=1.753792547514E9, endTime=null, isSealed=false, duration=-1))
2025-07-29 18:35:47.514 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.d5 on 2 subscribers.
2025-07-29 18:35:47.514 27460-27592 Braze v24.3.0 .z0       org.levimc.launcher                  D  Session start event for new session received.
2025-07-29 18:35:47.515 27460-27592 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 4f989e4b-1d77-4b12-a767-fe2f534213c5
2025-07-29 18:35:47.515 27460-27592 Braze v24.3.0 .u        org.levimc.launcher                  D  Checking if this session needs to be sealed: 4f989e4b-1d77-4b12-a767-fe2f534213c5
2025-07-29 18:35:47.515 27460-27592 Braze v24.3.0 .p        org.levimc.launcher                  V  Attempting to log event: {
                                                                                                      "name": "ss",
                                                                                                      "data": {},
                                                                                                      "time": 1.753792547514E9,
                                                                                                      "user_id": "7115A474BB854844",
                                                                                                      "session_id": "4f989e4b-1d77-4b12-a767-fe2f534213c5"
                                                                                                    }
2025-07-29 18:35:47.516 27460-27591 Braze v24.3.0 .j5       org.levimc.launcher                  D  Adding event to storage with uid 672a31e4-973b-4b1c-981c-5ba4b8de038d
2025-07-29 18:35:47.518 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = ADD_BRAZE_EVENT
                                                                                                    brazeEvent = {"name":"ss","data":{},"time":1.753792547514E9,"user_id":"7115A474BB854844","session_id":"4f989e4b-1d77-4b12-a767-fe2f534213c5"}
                                                                                                    sessionId = null
                                                                                                    brazeRequest = null
2025-07-29 18:35:47.518 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:35:47.521 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired: commandType = FLUSH_PENDING_BRAZE_EVENTS
                                                                                                    brazeEvent = null
                                                                                                    sessionId = 4f989e4b-1d77-4b12-a767-fe2f534213c5
                                                                                                    brazeRequest = null
2025-07-29 18:35:47.521 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:35:47.521 27460-27592 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: true
2025-07-29 18:35:47.521 27460-27592 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-29 18:35:47.521 27460-27592 Braze v24.3.0 .l0       org.levimc.launcher                  V  Device object cache cleared.
2025-07-29 18:35:47.521 27460-27592 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting trigger refresh.
2025-07-29 18:35:47.528 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_server_target'. Using default value 'PROD'.
2025-07-29 18:35:47.528 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_server_target' and value: 'PROD'
2025-07-29 18:35:47.531 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:35:47.531 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:35:47.532 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.y5 fired: TriggerDispatchStartedEvent(request={
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:47.532 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.y5 on 1 subscribers.
2025-07-29 18:35:47.532 27460-27592 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:35:47.533 27460-27592 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-29 18:35:47.533 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_automatic_geofence_requests_enabled'. Using default value 'true'.
2025-07-29 18:35:47.534 27460-27592 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_automatic_geofence_requests_enabled' and value: 'true'
2025-07-29 18:35:47.534 27460-27592 Braze v24.3.0 .z0       org.levimc.launcher                  D  Requesting Braze Geofence refresh on session created event due to configuration.
2025-07-29 18:35:47.534 27460-27592 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Content Card refresh on session created event due to server configuration.
2025-07-29 18:35:47.534 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_sdk_authentication_enabled'. Using default value 'false'.
2025-07-29 18:35:47.534 27460-27592 Braze v24.3.0 .z0       org.levimc.launcher                  D  Not automatically requesting Feature Flags refresh on session created event due to server configuration.
2025-07-29 18:35:47.534 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  com.braze.events.SessionStateChangedEvent fired: SessionStateChangedEvent{sessionId='4f989e4b-1d77-4b12-a767-fe2f534213c5', eventType='SESSION_STARTED'}'
2025-07-29 18:35:47.534 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_authentication_enabled' and value: 'false'
2025-07-29 18:35:47.534 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  I  Event was published, but no subscribers were found. Saving event for later publishing to a matching subscriber. Event class: class com.braze.events.SessionStateChangedEvent
2025-07-29 18:35:47.535 27460-27589 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-29 18:35:47.536 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING configuration value with primary key 'com_braze_sdk_flavor'. Using default value 'null'.
2025-07-29 18:35:47.537 27460-27592 Braze v24.3.0 .u        org.levimc.launcher                  D  Creating a session seal alarm with a delay of 10000 ms
2025-07-29 18:35:47.537 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_flavor' and value: 'null'
2025-07-29 18:35:47.546 27460-27583 CompatChangeReporter    org.levimc.launcher                  D  Compat change id reported: 312399441; UID 10637; state: ENABLED
2025-07-29 18:35:47.547 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.i5 fired: bo.app.i5@587e359
2025-07-29 18:35:47.547 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.i5 on 1 subscribers.
2025-07-29 18:35:47.547 27460-27592 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-29 18:35:47.547 27460-27592 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:35:47.547 27460-27592 Braze v24.3.0 .u        org.levimc.launcher                  D  Closed session with id 4f989e4b-1d77-4b12-a767-fe2f534213c5
2025-07-29 18:35:47.548 27460-27592 Braze v24.3.0 .Braze    org.levimc.launcher                  V  requestImmediateDataFlush() called
2025-07-29 18:35:47.548 27460-27592 Braze v24.3.0 .a5       org.levimc.launcher                  V  Not allowing server config info unlock. Returning null.
2025-07-29 18:35:47.549 27460-27589 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@1f71b1e
2025-07-29 18:35:47.550 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844"
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:35:47.550 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:35:47.550 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_device_object_whitelisting_enabled'. Using default value 'false'.
2025-07-29 18:35:47.551 27460-27592 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844"
                                                                                                      }
                                                                                                    }
2025-07-29 18:35:47.551 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_device_object_whitelisting_enabled' and value: 'false'
2025-07-29 18:35:47.551 27460-27592 Braze v24....nceManager org.levimc.launcher                  D  Braze geofences not enabled. Not requesting geofences.
2025-07-29 18:35:47.551 27460-27589 Braze v24.3.0 .l0       org.levimc.launcher                  V  Sending full device due to NOTIFICATIONS_ENABLED true
2025-07-29 18:35:47.551 27460-27589 Braze v24.3.0 .l0       org.levimc.launcher                  V  Remote Notification setting changed to true. Updating user subscription.
2025-07-29 18:35:47.552 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml BOOLEAN configuration value with primary key 'com_braze_device_object_whitelisting_enabled'. Using default value 'false'.
2025-07-29 18:35:47.553 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_device_object_whitelisting_enabled' and value: 'false'
2025-07-29 18:35:47.553 27460-27589 Braze v24.3.0 .m6       org.levimc.launcher                  V  Push token cache cleared.
2025-07-29 18:35:47.553 27460-27589 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@7addfff
2025-07-29 18:35:47.553 27460-27589 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-29 18:35:47.554 27460-27589 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"ss","data":{},"time":1.753792547514E9,"user_id":"7115A474BB854844","session_id":"4f989e4b-1d77-4b12-a767-fe2f534213c5"} with uid: 672a31e4-973b-4b1c-981c-5ba4b8de038d
2025-07-29 18:35:47.554 27460-27589 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"se","data":{"d":45},"time":1.753792547489E9,"user_id":"7115A474BB854844","session_id":"7938609f-cb95-49a8-b324-d9d567c4cd27"} with uid: 4fb1e208-703b-453b-8a40-ec9fbc7d52f3
2025-07-29 18:35:47.554 27460-27589 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"se","data":{"d":23},"time":1.753792501788E9,"user_id":"7115A474BB854844","session_id":"05fe2783-0bfe-481d-966d-6ceba0a5ceba"} with uid: 262337e1-692b-4964-9cfe-546c28733e20
2025-07-29 18:35:47.554 27460-27589 Braze v24.3.0 .r0       org.levimc.launcher                  D  Event dispatched: {"name":"ss","data":{},"time":1.753792501792E9,"user_id":"7115A474BB854844","session_id":"7938609f-cb95-49a8-b324-d9d567c4cd27"} with uid: 4a7b4ee4-2fc7-4521-b519-0eeae9578294
2025-07-29 18:35:47.554 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_internal_sdk_metadata'. Using default value '[]'.
2025-07-29 18:35:47.555 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_internal_sdk_metadata' and value: '[]'
2025-07-29 18:35:47.555 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Unable to find the xml STRING_ARRAY configuration value with primary key 'com_braze_sdk_metadata'. Using default value '[]'.
2025-07-29 18:35:47.555 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Using resources value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-29 18:35:47.555 27460-27589 Braze v24....onProvider org.levimc.launcher                  D  Using runtime override value for key: 'com_braze_sdk_metadata' and value: '[]'
2025-07-29 18:35:47.557 27460-27589 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-29 18:35:47.559 27460-27589 Braze v24.3.0 .l0       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-29 18:35:47.560 27460-27589 Braze v24.3.0 .m6       org.levimc.launcher                  D  Received call to export dirty object, but the cache was already locked.
2025-07-29 18:35:47.560 27460-27589 Braze v24.3.0 .t0       org.levimc.launcher                  D  Short circuiting execution of network request and immediately marking it as succeeded.
2025-07-29 18:35:47.561 27460-27589 Braze v24.3.0 .j0       org.levimc.launcher                  D  DataSyncRequest executed successfully.
2025-07-29 18:35:47.562 27460-27589 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792547,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844"
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:47.562 27460-27589 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-29 18:35:47.589 27460-27590 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "a527860257292594"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "X-Braze-TriggersRequest" => "true"
                                                                                                    "X-Braze-DataRequest" => "true"
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792547,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792547514E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4f989e4b-1d77-4b12-a767-fe2f534213c5"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 45
                                                                                                          },
                                                                                                          "time": 1.753792547489E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 23
                                                                                                          },
                                                                                                          "time": 1.753792501788E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "05fe2783-0bfe-481d-966d-6ceba0a5ceba"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792501792E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:35:47.617 27460-27571 BitmapFactory           org.levimc.launcher                  E  Unable to decode file: java.io.FileNotFoundException: /data/user/0/org.levimc.launcher/games/com.mojang/minecraftpe/custom.png: open failed: ENOENT (No such file or directory)
2025-07-29 18:35:47.617 27460-27571 System.err              org.levimc.launcher                  W  getImageData: Could not open image /data/user/0/org.levimc.launcher/games/com.mojang/minecraftpe/custom.png
2025-07-29 18:35:47.667 27460-27590 Braze v24.3.0 .q5       org.levimc.launcher                  V  Enabling SSL protocols: [TLSv1.2, TLSv1.3]
2025-07-29 18:35:47.696 27460-27585 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:0,s:0) onFrameAvailable the first frame is available
2025-07-29 18:35:47.696 27460-27585 BLASTBufferQueue        org.levimc.launcher                  I  [SurfaceView[org.levimc.launcher/com.mojang.minecraftpe.Launcher]@0#4](f:0,a:1,s:0) acquireNextBufferLocked size=2340x1080 mFrameNumber=1 applyTransaction=true mTimestamp=272488872608703(auto) mPendingTransactions.size=0 graphicBufferId=117939801948199 transform=7
2025-07-29 18:35:48.068 27460-27590 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = a527860257292594 time = 478ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-29 18:35:48.070 27460-27590 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-29 18:35:48.070 27460-27590 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-29 18:35:48.070 27460-27590 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-29 18:35:48.071 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792547,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792547514E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4f989e4b-1d77-4b12-a767-fe2f534213c5"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 45
                                                                                                          },
                                                                                                          "time": 1.753792547489E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 23
                                                                                                          },
                                                                                                          "time": 1.753792501788E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "05fe2783-0bfe-481d-966d-6ceba0a5ceba"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792501792E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-29 18:35:48.071 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-29 18:35:48.071 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-29 18:35:48.072 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792547,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792547514E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4f989e4b-1d77-4b12-a767-fe2f534213c5"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 45
                                                                                                          },
                                                                                                          "time": 1.753792547489E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 23
                                                                                                          },
                                                                                                          "time": 1.753792501788E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "05fe2783-0bfe-481d-966d-6ceba0a5ceba"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792501792E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:48.072 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:35:48.073 27460-27590 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 7115A474BB854844
2025-07-29 18:35:48.074 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792547,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792547514E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4f989e4b-1d77-4b12-a767-fe2f534213c5"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 45
                                                                                                          },
                                                                                                          "time": 1.753792547489E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 23
                                                                                                          },
                                                                                                          "time": 1.753792501788E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "05fe2783-0bfe-481d-966d-6ceba0a5ceba"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792501792E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:48.074 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-29 18:35:48.075 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792547,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792547514E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4f989e4b-1d77-4b12-a767-fe2f534213c5"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 45
                                                                                                          },
                                                                                                          "time": 1.753792547489E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 23
                                                                                                          },
                                                                                                          "time": 1.753792501788E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "05fe2783-0bfe-481d-966d-6ceba0a5ceba"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792501792E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:48.075 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-29 18:35:48.076 27460-27590 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@1f71b1e
2025-07-29 18:35:48.076 27460-27590 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@7addfff
2025-07-29 18:35:48.077 27460-27590 Braze v24.3.0 .p        org.levimc.launcher                  V  Updated shouldRequestTriggersInNextRequest to: false
2025-07-29 18:35:48.077 27460-27590 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-29 18:35:48.077 27460-27590 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-29 18:35:48.077 27460-27590 Braze v24.3.0 .j0       org.levimc.launcher                  D  Trigger dispatch completed. Alerting subscribers.
2025-07-29 18:35:48.078 27460-27601 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 672a31e4-973b-4b1c-981c-5ba4b8de038d
2025-07-29 18:35:48.078 27460-27601 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 4fb1e208-703b-453b-8a40-ec9fbc7d52f3
2025-07-29 18:35:48.078 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.x5 fired: TriggerDispatchCompletedEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792547,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "device": {
                                                                                                        "os_version": "35",
                                                                                                        "carrier": "Banglalink",
                                                                                                        "model": "SM-A166P",
                                                                                                        "resolution": "1080x2340",
                                                                                                        "locale": "en_GB",
                                                                                                        "remote_notification_enabled": true,
                                                                                                        "android_is_background_restricted": false,
                                                                                                        "time_zone": "Asia\/Dhaka"
                                                                                                      },
                                                                                                      "attributes": [
                                                                                                        {
                                                                                                          "push_subscribe": "subscribed",
                                                                                                          "user_id": "7115A474BB854844"
                                                                                                        }
                                                                                                      ],
                                                                                                      "events": [
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792547514E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "4f989e4b-1d77-4b12-a767-fe2f534213c5"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 45
                                                                                                          },
                                                                                                          "time": 1.753792547489E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "se",
                                                                                                          "data": {
                                                                                                            "d": 23
                                                                                                          },
                                                                                                          "time": 1.753792501788E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "05fe2783-0bfe-481d-966d-6ceba0a5ceba"
                                                                                                        },
                                                                                                        {
                                                                                                          "name": "ss",
                                                                                                          "data": {},
                                                                                                          "time": 1.753792501792E9,
                                                                                                          "user_id": "7115A474BB854844",
                                                                                                          "session_id": "7938609f-cb95-49a8-b324-d9d567c4cd27"
                                                                                                        }
                                                                                                      ],
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "triggers": true,
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:48.078 27460-27601 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 262337e1-692b-4964-9cfe-546c28733e20
2025-07-29 18:35:48.078 27460-27590 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.x5 on 1 subscribers.
2025-07-29 18:35:48.079 27460-27601 Braze v24.3.0 .j5       org.levimc.launcher                  D  Deleting event from storage with uid 4a7b4ee4-2fc7-4521-b519-0eeae9578294
2025-07-29 18:35:48.079 27460-27590 Braze v24.....bo.app.d6 org.levimc.launcher                  D  In flight trigger requests is empty. Executing any pending trigger events.
2025-07-29 18:35:48.538 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:35:48.538 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:35:48.540 27460-27601 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:35:48.540 27460-27590 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-29 18:35:48.542 27460-27590 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@1f71b1e
2025-07-29 18:35:48.543 27460-27590 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@7addfff
2025-07-29 18:35:48.544 27460-27590 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-29 18:35:48.558 27460-27601 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "67e8d5edcce5f5fc"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792548,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:35:49.087 27460-27601 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = 67e8d5edcce5f5fc time = 529ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-29 18:35:49.087 27460-27601 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-29 18:35:49.087 27460-27601 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-29 18:35:49.088 27460-27601 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-29 18:35:49.088 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792548,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-29 18:35:49.088 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-29 18:35:49.088 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-29 18:35:49.088 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792548,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:49.088 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 7115A474BB854844
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792548,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:49.089 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-29 18:35:49.090 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792548,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:49.090 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-29 18:35:49.090 27460-27601 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@1f71b1e
2025-07-29 18:35:49.090 27460-27601 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@7addfff
2025-07-29 18:35:49.090 27460-27601 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-29 18:35:49.091 27460-27601 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-29 18:35:49.615 27460-27571 fmod                    org.levimc.launcher                  I  AudioDevice::init : Min buffer size: 8224 bytes
2025-07-29 18:35:49.615 27460-27571 fmod                    org.levimc.launcher                  I  AudioDevice::init : Actual buffer size: 8224 bytes
2025-07-29 18:35:49.644 27460-27571 AudioTrack              org.levimc.launcher                  W  Use of stream types is deprecated for operations other than volume control
2025-07-29 18:35:49.644 27460-27571 AudioTrack              org.levimc.launcher                  W  See the documentation of AudioTrack() for what to use instead with android.media.AudioAttributes to qualify your playback use case
2025-07-29 18:35:49.785 27460-27460 XALJAVA                 org.levimc.launcher                  V  [PresenceManager] XalLogger created.
2025-07-29 18:35:49.789 27460-27460 XALJAVA                 org.levimc.launcher                  W  [P][PresenceManager] Ignoring resume, not currently paused
2025-07-29 18:35:49.794 27460-27460 HttpCallStaticGlue      org.levimc.launcher                  D  Successfully registerered HttpCall methods
2025-07-29 18:35:49.794 27460-27460 XboxLiveAppConfig       org.levimc.launcher                  D  Successfully registerered XboxLiveAppConfig methods
2025-07-29 18:35:49.794 27460-27460 XSAPI.Android           org.levimc.launcher                  D  Successfully registerered HttpCall tcuiMethods
2025-07-29 18:35:49.796 27460-27460 Interop                 org.levimc.launcher                  I  locale is: en_GB
2025-07-29 18:35:49.883 27460-27584 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Lcom/android/org/conscrypt/OpenSSLProvider;-><init>()V (runtime_flags=CorePlatformApi, domain=core-platform, api=unsupported,core-platform-api) from Lorg/spongycastle/jcajce/provider/drbg/DRBG; (domain=app) using reflection: allowed
2025-07-29 18:35:49.886 27460-27584 levimc.launcher         org.levimc.launcher                  I  hiddenapi: Accessing hidden method Lcom/android/org/conscrypt/OpenSSLRandom;-><init>()V (runtime_flags=0, domain=core-platform, api=unsupported) from Lorg/spongycastle/jcajce/provider/drbg/DRBG; (domain=app) using reflection: allowed
2025-07-29 18:35:49.909 27460-27571 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Async Services Manager starting in Async mode
2025-07-29 18:35:50.196 27460-27571 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using External dir (NEW) - CurrentFileStoragePath is now '/storage/emulated/0/Android/data/org.levimc.launcher/files'
2025-07-29 18:35:50.209 27460-27571 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - AppPlatform_android::setStorageDirectory - using AppData dir - CurrentFileStoragePath is now '/data/user/0/org.levimc.launcher'
2025-07-29 18:35:50.360 27460-27465 levimc.launcher         org.levimc.launcher                  I  NativeAlloc concurrent mark compact GC freed 7565KB AllocSpace bytes, 58(1624KB) LOS objects, 43% free, 30MB/54MB, paused 369us,12.297ms total 69.497ms
2025-07-29 18:35:50.361 27460-27467 System                  org.levimc.launcher                  W  A resource failed to call ZipFile.close. 
2025-07-29 18:35:50.362 27460-27466 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=155
2025-07-29 18:35:50.363 27460-27466 InputTransport          org.levimc.launcher                  D  Input channel destroyed: 'ClientS', fd=150
2025-07-29 18:35:50.369 27460-27467 System                  org.levimc.launcher                  W  A resource failed to call close. 
2025-07-29 18:35:52.472 27460-27571 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Time played notifier not required for 'en_GB'
2025-07-29 18:35:57.542 27460-27602 Braze v24....eCoroutine org.levimc.launcher                  D  Requesting data flush on internal session close flush timer.
2025-07-29 18:35:57.545 27460-27592 Braze v24.3.0 .Braze    org.levimc.launcher                  V  requestImmediateDataFlush() called
2025-07-29 18:35:57.548 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.p0 fired:             commandType = ADD_REQUEST
                                                                                                                brazeEvent = null
                                                                                                                sessionId = null
                                                                                                                brazeRequest = {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:35:57.548 27460-27592 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.p0 on 1 subscribers.
2025-07-29 18:35:57.549 27460-27592 Braze v24.3.0 .r0       org.levimc.launcher                  V  Added request to dispatcher with parameters: 
                                                                                                    {
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:35:57.550 27460-27602 Braze v24.3.0 .r0       org.levimc.launcher                  V  SDK Auth is disabled, not adding signature to request
2025-07-29 18:35:57.553 27460-27602 Braze v24.3.0 .l0       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.l0@1f71b1e
2025-07-29 18:35:57.554 27460-27602 Braze v24.3.0 .m6       org.levimc.launcher                  D  Cache locked successfully for export: bo.app.m6@7addfff
2025-07-29 18:35:57.554 27460-27602 Braze v24.3.0 .m6       org.levimc.launcher                  D  No push token available to add to attributes object.
2025-07-29 18:35:57.563 27460-27601 Braze v24.3.0 .i3       org.levimc.launcher                  D  Making request with id => "f5816c72e55de9a0"
                                                                                                    to url: https://sdk.iad-01.braze.com/api/v3/data
                                                                                                                
                                                                                                    with headers:
                                                                                                    "Accept-Encoding" => "gzip, deflate"
                                                                                                    "Content-Type" => "application/json"
                                                                                                    "X-Braze-Api-Key" => "7e90f2bd-d27b-4010-a501-a8e30021418a"
                                                                                                    
                                                                                                    and JSON :
                                                                                                    {
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792557,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
2025-07-29 18:35:57.905 27460-27601 Braze v24.3.0 .i3       org.levimc.launcher                  D  Result(id = f5816c72e55de9a0 time = 341ms)
                                                                                                    {
                                                                                                      "error": "invalid_api_key"
                                                                                                    }
2025-07-29 18:35:57.905 27460-27601 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Triggered actions Json array was null. Not de-serializing triggered actions.
2025-07-29 18:35:57.905 27460-27601 Braze v24.....bo.app.f6 org.levimc.launcher                  D  Templated message Json was null. Not de-serializing templated message.
2025-07-29 18:35:57.905 27460-27601 Braze v24.3.0 .t        org.levimc.launcher                  W  Received server error from request: invalid_api_key
2025-07-29 18:35:57.906 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.b5 fired: ServerResponseErrorEvent(responseError=InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792557,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data))
2025-07-29 18:35:57.907 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.b5 on 1 subscribers.
2025-07-29 18:35:57.907 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  Error occurred while executing Braze request: invalid_api_key
2025-07-29 18:35:57.908 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.g3 fired: InvalidApiKeyError(errorMessage=invalid_api_key, originalRequest={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792557,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:57.908 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.g3 on 1 subscribers.
2025-07-29 18:35:57.908 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:35:57.908 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  **                        !! WARNING !!                         **
2025-07-29 18:35:57.908 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  **  The current API key/endpoint combination is invalid. This   **
2025-07-29 18:35:57.908 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  ** is potentially an integration error. Please ensure that your **
2025-07-29 18:35:57.909 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  **     API key AND custom endpoint information are correct.     **
2025-07-29 18:35:57.909 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> API key    : 7e90f2bd-d27b-4010-a501-a8e30021418a
2025-07-29 18:35:57.909 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  >> Request Uri: https://sdk.iad-01.braze.com/api/v3/data
2025-07-29 18:35:57.909 27460-27601 Braze v24.3.0 .j0       org.levimc.launcher                  W  ******************************************************************
2025-07-29 18:35:57.909 27460-27601 Braze v24.3.0 .t        org.levimc.launcher                  V  Processing server response payload for user with id: 7115A474BB854844
2025-07-29 18:35:57.910 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.q4 fired: RequestNetworkSuccessEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792557,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:57.911 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.q4 on 1 subscribers.
2025-07-29 18:35:57.912 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  bo.app.s0 fired: DispatchSucceededEvent(request={
                                                                                                      "device_id": "c8091a71-f985-4d73-8b86-fffce44c7330",
                                                                                                      "time": 1753792557,
                                                                                                      "api_key": "7e90f2bd-d27b-4010-a501-a8e30021418a",
                                                                                                      "sdk_version": "24.3.0",
                                                                                                      "app_version": "1.0.12",
                                                                                                      "app_version_code": "18124512.0.0.0",
                                                                                                      "respond_with": {
                                                                                                        "user_id": "7115A474BB854844",
                                                                                                        "config": {
                                                                                                          "config_time": 0
                                                                                                        }
                                                                                                      }
                                                                                                    }
                                                                                                    to target: https://sdk.iad-01.braze.com/api/v3/data)
2025-07-29 18:35:57.912 27460-27601 Braze v24.3.0 .a1       org.levimc.launcher                  D  Triggering bo.app.s0 on 1 subscribers.
2025-07-29 18:35:57.913 27460-27601 Braze v24.3.0 .l0       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.l0@1f71b1e
2025-07-29 18:35:57.913 27460-27601 Braze v24.3.0 .m6       org.levimc.launcher                  V  Notifying confirmAndUnlock listeners for cache: bo.app.m6@7addfff
2025-07-29 18:35:57.914 27460-27601 Braze v24.3.0 .a5       org.levimc.launcher                  V  Attempting to unlock server config info.
2025-07-29 18:35:57.914 27460-27601 Braze v24.3.0 .a5       org.levimc.launcher                  D  Unlocking config info lock.
2025-07-29 18:35:58.648 27460-27592 Braze v24.3.0 .Braze    org.levimc.launcher                  I  Received request to change current user 7115A474BB854844 to the same user id. Not changing user.
2025-07-29 18:35:59.606 27460-27465 levimc.launcher         org.levimc.launcher                  I  NativeAlloc concurrent mark compact GC freed 7120KB AllocSpace bytes, 7(264KB) LOS objects, 43% free, 31MB/55MB, paused 381us,10.955ms total 55.828ms
2025-07-29 18:35:59.912 27460-27578 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [27460] IntegrityService : requestIntegrityToken(IntegrityTokenRequest{nonce=MjBDQTIuNTBDNjczQjdBNERFQzM1QQ, cloudProjectNumber=null})
2025-07-29 18:35:59.917 27460-27754 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [27460] IntegrityService : Initiate binding to the service.
2025-07-29 18:35:59.927 27460-27460 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [27460] IntegrityService : ServiceConnectionImpl.onServiceConnected(ComponentInfo{com.android.vending/com.google.android.finsky.integrityservice.IntegrityService})
2025-07-29 18:35:59.929 27460-27754 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [27460] IntegrityService : linkToDeath
2025-07-29 18:36:01.492  3729-3952  Finsky                  com.android.vending                  E  [1781] requestIntegrityToken() failed for org.levimc.launcher.
                                                                                                    com.google.android.finsky.integrityservice.IntegrityException
                                                                                                    	at yyr.a(PG:82)
                                                                                                    	at yyp.iK(PG:22)
                                                                                                    	at maa.j(PG:9)
                                                                                                    	at mzz.j(PG:22)
                                                                                                    	at lzu.run(PG:31)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:959)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:100)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:257)
                                                                                                    	at android.os.Looper.loop(Looper.java:342)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:9638)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:619)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:929)
                                                                                                    Caused by: DisplayErrorMessage[Error while retrieving information from server. DF-DFERH-01]
                                                                                                    	at mzz.kq(PG:40)
                                                                                                    	at lzy.a(PG:204)
                                                                                                    	at lzy.run(PG:6)
2025-07-29 18:36:01.497 27460-27754 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [27460] IntegrityService : Unbind from service.
2025-07-29 18:36:01.497 27460-27472 PlayCore                org.levimc.launcher                  I  UID: [10637]  PID: [27460] OnRequestIntegrityTokenCallback : onRequestIntegrityToken
2025-07-29 18:36:23.522 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:36:23.523 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=high hint, reason=touch, vri=VRI[Launcher]@80d8512
2025-07-29 18:36:23.661 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:36:26.068 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  ViewPostIme pointer 0
2025-07-29 18:36:26.158 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  ViewPostIme pointer 1
2025-07-29 18:36:26.242 27460-27580 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Opening level '/data/user/0/org.levimc.launcher/games/com.mojang/minecraftWorlds/f9F86PQb7VY=/db'
2025-07-29 18:36:26.360 27460-27580 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - [SERVER] Pack Stack - None
2025-07-29 18:36:29.164 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  call setFrameRateCategory for touch hint category=no preference, reason=boost timeout, vri=VRI[Launcher]@80d8512
2025-07-29 18:36:29.852 27460-27579 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-29 18:36:29.853 27460-27579 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:36:31.111 27460-27460 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@80d8512 mNativeObject= 0xb4000078ea2adc00 sc.mNativeObject= 0xb4000078e9ef4ac0 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:36:31.111 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=0 res=0x0 s={true 0xb4000078ea4c1000} ch=false seqId=0
2025-07-29 18:36:31.111 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  updateBoundsLayer: t=android.view.SurfaceControl$Transaction@2b75e22 sc=Surface(name=Bounds for - org.levimc.launcher/com.mojang.minecraftpe.Launcher@0)/@0x70da0b3 frame=5
2025-07-29 18:36:31.111 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-29 18:36:31.113 27460-27519 VRI[Launcher]@80d8512   org.levimc.launcher                  I  mWNT: t=0xb4000078ea500300 mBlastBufferQueue=0xb4000078ea2adc00 fn= 5 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-29 18:36:31.174 27460-27460 BLASTBufferQueue_Java   org.levimc.launcher                  I  update, w= 2340 h= 1080 mName = VRI[Launcher]@80d8512 mNativeObject= 0xb4000078ea2adc00 sc.mNativeObject= 0xb4000078e9ef4ac0 format= -3 caller= android.view.ViewRootImpl.updateBlastSurfaceIfNeeded:3386 android.view.ViewRootImpl.relayoutWindow:11361 android.view.ViewRootImpl.performTraversals:4544 android.view.ViewRootImpl.doTraversal:3708 android.view.ViewRootImpl$TraversalRunnable.run:12542 android.view.Choreographer$CallbackRecord.run:1751 
2025-07-29 18:36:31.174 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  Relayout returned: old=(0,0,2340,1080) new=(0,0,2340,1080) relayoutAsync=true req=(2340,1080)0 dur=0 res=0x0 s={true 0xb4000078ea4c1000} ch=false seqId=0
2025-07-29 18:36:31.174 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  updateBoundsLayer: t=android.view.SurfaceControl$Transaction@2b75e22 sc=Surface(name=Bounds for - org.levimc.launcher/com.mojang.minecraftpe.Launcher@0)/@0x70da0b3 frame=6
2025-07-29 18:36:31.175 27460-27460 VRI[Launcher]@80d8512   org.levimc.launcher                  I  registerCallbackForPendingTransactions
2025-07-29 18:36:31.177 27460-27520 VRI[Launcher]@80d8512   org.levimc.launcher                  I  mWNT: t=0xb40000786f669c00 mBlastBufferQueue=0xb4000078ea2adc00 fn= 6 HdrRenderState mRenderHdrSdrRatio=1.0 caller= android.view.ViewRootImpl$9.onFrameDraw:6276 android.view.ViewRootImpl$3.onFrameDraw:2440 android.view.ThreadedRenderer$1.onFrameDraw:761 
2025-07-29 18:36:31.250 27460-27875 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Player connected: Kai, xuid: 
2025-07-29 18:36:32.846 27460-27579 Braze v24.3.0 .i0       org.levimc.launcher                  V  recalculateDispatchState called with session state: NO_SESSION lastNetworkLevel: GOOD
2025-07-29 18:36:32.846 27460-27579 Braze v24.3.0 .i0       org.levimc.launcher                  V  currentIntervalMs: -1
2025-07-29 18:36:37.097 27460-27586 Minecraft               org.levimc.launcher                  V  NO LOG FILE! - DBStorage - Compaction Starting...
2025-07-29 18:36:37.154 27460-27586 Minecraft               org.levimc.launcher                  V  NO LOG FILE! - DBStorage - Compaction Complete.
2025-07-29 18:36:37.154 27460-27586 Minecraft               org.levimc.launcher                  V  NO LOG FILE! - Scheduling AutoCompaction...
2025-07-29 18:36:42.468 27460-27875 Minecraft               org.levimc.launcher                  I  NO LOG FILE! - Player Spawned: Kai xuid: , pfid: 7115a474bb854844