{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkver = "21",
            cross = "arm-linux-androideabi-",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            ndkver = 25
        }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program_fetch_package_xmake = {
        cmake = false,
        ninja = false
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            license = "MIT",
            version = "v3.11.3"
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            version = "10.2.1",
            license = "MIT",
            links = {
                "fmt"
            },
            static = true
        }
    },
    find_program = {
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        tar = [[C:\Windows\System32\tar.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        git = [[C:\Program Files\Git\cmd\git.exe]],
        clang = false,
        nim = false,
        zig = false
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-keep-static-consts"] = true,
            ["-x"] = true,
            ["-fstack-protector"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-imacros"] = true,
            ["-mgpopt"] = true,
            ["-ffixed-x15"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-cl-opt-disable"] = true,
            ["-fcall-saved-x11"] = true,
            ["-mno-crc"] = true,
            ["-gdwarf-3"] = true,
            ["-Xopenmp-target"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["--config"] = true,
            ["-ffixed-d3"] = true,
            ["-fcoroutines-ts"] = true,
            ["-print-search-dirs"] = true,
            ["-fms-compatibility"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fmodules"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-ffixed-x31"] = true,
            ["-Tbss"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-iquote"] = true,
            ["-ffixed-x26"] = true,
            ["-v"] = true,
            ["-ffixed-x12"] = true,
            ["-mmsa"] = true,
            ["-fno-access-control"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fsigned-char"] = true,
            ["-MQ"] = true,
            ["-mmadd4"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-ffixed-a6"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-print-effective-triple"] = true,
            ["-fintegrated-cc1"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-fno-builtin"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-mno-embedded-data"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fenable-matrix"] = true,
            ["-mllvm"] = true,
            ["-mfentry"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-nostdinc"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-ffixed-x3"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-gline-directives-only"] = true,
            ["-mpackets"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-emit-ast"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fexceptions"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fvectorize"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-mno-madd4"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-mno-global-merge"] = true,
            ["-ffixed-point"] = true,
            ["-frwpi"] = true,
            ["-print-runtime-dir"] = true,
            ["-Qy"] = true,
            ["-ffixed-x22"] = true,
            ["-fno-cxx-modules"] = true,
            ["-module-dependency-dir"] = true,
            ["-U"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-MJ"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-ffixed-d4"] = true,
            ["-mno-long-calls"] = true,
            ["-ffixed-a0"] = true,
            ["--no-cuda-version-check"] = true,
            ["-ffunction-sections"] = true,
            ["-ffixed-d6"] = true,
            ["-relocatable-pch"] = true,
            ["-cl-mad-enable"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-ftrapv"] = true,
            ["-T"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fno-strict-return"] = true,
            ["-mmemops"] = true,
            ["-fuse-line-directives"] = true,
            ["-fstrict-enums"] = true,
            ["-fshort-wchar"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fblocks"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-fopenmp"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-faddrsig"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-mthread-model"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-mfp32"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fno-finite-loops"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-gdwarf-4"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fpch-codegen"] = true,
            ["-mno-execute-only"] = true,
            ["-time"] = true,
            ["-mcmse"] = true,
            ["-ffast-math"] = true,
            ["-pg"] = true,
            ["-ffixed-x10"] = true,
            ["-ffixed-x13"] = true,
            ["-Wdeprecated"] = true,
            ["-fverbose-asm"] = true,
            ["-fno-trigraphs"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-ffixed-x27"] = true,
            ["-finline-functions"] = true,
            ["--migrate"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mextern-sdata"] = true,
            ["-mhvx-qfloat"] = true,
            ["-MMD"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-mlvi-cfi"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fsplit-stack"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fcall-saved-x14"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fgpu-sanitize"] = true,
            ["-ffixed-x17"] = true,
            ["-fseh-exceptions"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-ffixed-d0"] = true,
            ["-fstack-protector-all"] = true,
            ["-fgpu-rdc"] = true,
            ["-static-libsan"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-miamcu"] = true,
            ["-Xanalyzer"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["--analyzer-output"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fsave-optimization-record"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fgnu-runtime"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fsanitize-trap"] = true,
            ["-S"] = true,
            ["--hip-link"] = true,
            ["-fropi"] = true,
            ["-mstackrealign"] = true,
            ["--help-hidden"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fxray-instrument"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-dsym-dir"] = true,
            ["-fno-rtti"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fintegrated-as"] = true,
            ["-fjump-tables"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-lto"] = true,
            ["-munaligned-access"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-mabicalls"] = true,
            ["-fobjc-weak"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-idirafter"] = true,
            ["-trigraphs"] = true,
            ["-L"] = true,
            ["-fxray-link-deps"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-moutline-atomics"] = true,
            ["-fno-temp-file"] = true,
            ["-ffixed-x2"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fgnu89-inline"] = true,
            ["-mno-implicit-float"] = true,
            ["-CC"] = true,
            ["-ivfsoverlay"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fcoverage-mapping"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-M"] = true,
            ["-mno-nvs"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fcf-protection"] = true,
            ["-ffixed-a4"] = true,
            ["-mno-relax"] = true,
            ["-fstandalone-debug"] = true,
            ["-mrtd"] = true,
            ["-Xpreprocessor"] = true,
            ["-mno-memops"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fpascal-strings"] = true,
            ["-mfp64"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-dI"] = true,
            ["-mnocrc"] = true,
            ["--emit-static-lib"] = true,
            ["-z"] = true,
            ["-maix-struct-return"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fcall-saved-x18"] = true,
            ["-dependency-dot"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-Xclang"] = true,
            ["-ffixed-x28"] = true,
            ["-fshort-enums"] = true,
            ["-mmt"] = true,
            ["-fopenmp-simd"] = true,
            ["-fsized-deallocation"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-print-ivar-layout"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-funroll-loops"] = true,
            ["-ffixed-x9"] = true,
            ["-ffixed-x20"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fsystem-module"] = true,
            ["-mglobal-merge"] = true,
            ["-msave-restore"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-P"] = true,
            ["-rpath"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-pipe"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fcall-saved-x9"] = true,
            ["-isysroot"] = true,
            ["-gcodeview"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-extract-api"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-mrestrict-it"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fxray-ignore-loops"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fslp-vectorize"] = true,
            ["-mexecute-only"] = true,
            ["-MV"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fno-plt"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-o"] = true,
            ["-fno-autolink"] = true,
            ["-nogpulib"] = true,
            ["-fnew-infallible"] = true,
            ["-fno-global-isel"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-mlocal-sdata"] = true,
            ["-fno-spell-checking"] = true,
            ["-isystem"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-jump-tables"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-ffixed-d7"] = true,
            ["-mno-unaligned-access"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-mlvi-hardening"] = true,
            ["-rewrite-objc"] = true,
            ["-freciprocal-math"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mpacked-stack"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fdiscard-value-names"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-mno-gpopt"] = true,
            ["--verify-debug-info"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-debug-macro"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-finline-hint-functions"] = true,
            ["-fmodules-decluse"] = true,
            ["-fms-extensions"] = true,
            ["-D"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fapprox-func"] = true,
            ["-working-directory"] = true,
            ["-Xassembler"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fno-sycl"] = true,
            ["-foffload-lto"] = true,
            ["-nohipwrapperinc"] = true,
            ["-mno-abicalls"] = true,
            ["-emit-llvm"] = true,
            ["-fno-show-column"] = true,
            ["-ffixed-r9"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fignore-exceptions"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fcall-saved-x8"] = true,
            ["-c"] = true,
            ["-fstack-size-section"] = true,
            ["-mcrc"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-mlong-double-128"] = true,
            ["-msoft-float"] = true,
            ["-ffixed-x23"] = true,
            ["-fembed-bitcode"] = true,
            ["-gdwarf64"] = true,
            ["-gdwarf"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-MM"] = true,
            ["-freroll-loops"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-dependency-file"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fno-rtti-data"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-ffixed-x14"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-undef"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-mno-msa"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-cxx-isystem"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-mno-cumode"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-save-temps"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fzvector"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-mnvj"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fno-short-wchar"] = true,
            ["-fno-stack-protector"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-x16"] = true,
            ["-fcall-saved-x10"] = true,
            ["-mrecord-mcount"] = true,
            ["-ffixed-a5"] = true,
            ["-mno-extern-sdata"] = true,
            ["-mnop-mcount"] = true,
            ["-print-multiarch"] = true,
            ["-flto"] = true,
            ["-mrelax"] = true,
            ["-fms-hotpatch"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-index-header-map"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-verify-pch"] = true,
            ["-fno-show-source-location"] = true,
            ["-print-supported-cpus"] = true,
            ["-fstack-usage"] = true,
            ["-gno-embed-source"] = true,
            ["-ffixed-x30"] = true,
            ["-mmark-bti-property"] = true,
            ["-pthread"] = true,
            ["-arch"] = true,
            ["--cuda-host-only"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fcall-saved-x12"] = true,
            ["-nogpuinc"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fno-elide-type"] = true,
            ["-H"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-fmodules-search-all"] = true,
            ["--precompile"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-nobuiltininc"] = true,
            ["-ffixed-a3"] = true,
            ["-mno-hvx"] = true,
            ["-MP"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-mno-nvj"] = true,
            ["-E"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fprotect-parens"] = true,
            ["-fpcc-struct-return"] = true,
            ["-femulated-tls"] = true,
            ["-finstrument-functions"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fno-new-infallible"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-msvr4-struct-return"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fno-unique-section-names"] = true,
            ["-MT"] = true,
            ["-mcumode"] = true,
            ["-ffixed-x6"] = true,
            ["-ffixed-x7"] = true,
            ["-malign-double"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-dD"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fno-exceptions"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-ffixed-x21"] = true,
            ["-Qn"] = true,
            ["-ffinite-loops"] = true,
            ["-Tdata"] = true,
            ["-fno-declspec"] = true,
            ["-fasync-exceptions"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-ftime-trace"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-mno-seses"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-addrsig"] = true,
            ["-fdebug-types-section"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fapple-kext"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-ffixed-x25"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-MG"] = true,
            ["-iprefix"] = true,
            ["-mno-packets"] = true,
            ["-ffixed-r19"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-isystem-after"] = true,
            ["-MF"] = true,
            ["-dM"] = true,
            ["-B"] = true,
            ["-fgnu-keywords"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-mno-save-restore"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-mwavefrontsize64"] = true,
            ["-module-file-info"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-b"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-static-openmp"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fno-operator-names"] = true,
            ["-moutline"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-faligned-allocation"] = true,
            ["-mno-restrict-it"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["--cuda-device-only"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-help"] = true,
            ["-print-targets"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-serialize-diagnostics"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-ffixed-x24"] = true,
            ["-femit-all-decls"] = true,
            ["-Xlinker"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-C"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fno-offload-lto"] = true,
            ["-fno-fixed-point"] = true,
            ["-I-"] = true,
            ["-ffixed-d5"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-ffixed-x11"] = true,
            ["-fmath-errno"] = true,
            ["-fno-use-init-array"] = true,
            ["-include-pch"] = true,
            ["-gdwarf32"] = true,
            ["-MD"] = true,
            ["-w"] = true,
            ["-fprofile-generate"] = true,
            ["-gdwarf-5"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-fdeclspec"] = true,
            ["-fapplication-extension"] = true,
            ["-ffixed-d1"] = true,
            ["-mtgsplit"] = true,
            ["-fno-common"] = true,
            ["-fno-digraphs"] = true,
            ["-mlong-double-80"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-mno-mt"] = true,
            ["-ffixed-x4"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fglobal-isel"] = true,
            ["-fdata-sections"] = true,
            ["-fdebug-macro"] = true,
            ["-mnvs"] = true,
            ["-mms-bitfields"] = true,
            ["-ibuiltininc"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-traditional-cpp"] = true,
            ["-gembed-source"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-emit-module"] = true,
            ["-fmemory-profile"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fborland-extensions"] = true,
            ["-iwithsysroot"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-mno-outline-atomics"] = true,
            ["-fcs-profile-generate"] = true,
            ["-mlong-calls"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-gline-tables-only"] = true,
            ["-ffixed-x8"] = true,
            ["-membedded-data"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-print-target-triple"] = true,
            ["-meabi"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fno-split-stack"] = true,
            ["-fdigraphs"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fsanitize-stats"] = true,
            ["-mqdsp6-compat"] = true,
            ["-mbackchain"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fno-signed-char"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-iwithprefix"] = true,
            ["-fno-integrated-as"] = true,
            ["-mlong-double-64"] = true,
            ["-fobjc-arc"] = true,
            ["-mno-movt"] = true,
            ["-ffixed-d2"] = true,
            ["-ffreestanding"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fno-discard-value-names"] = true,
            ["-F"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mseses"] = true,
            ["-fpch-debuginfo"] = true,
            ["-mcode-object-v3"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-g"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-mhvx"] = true,
            ["-I"] = true,
            ["-include"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-Ttext"] = true,
            ["-pedantic"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-gmodules"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-fno-profile-generate"] = true,
            ["-ffixed-x5"] = true,
            ["-ffixed-x19"] = true,
            ["-ffixed-x29"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-gcodeview-ghash"] = true,
            ["-mrelax-all"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fcommon"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-G"] = true,
            ["-ffixed-a2"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-shared-libsan"] = true,
            ["--analyze"] = true,
            ["-mibt-seal"] = true,
            ["-print-resource-dir"] = true,
            ["-freg-struct-return"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-save-stats"] = true,
            ["-fcxx-modules"] = true,
            ["-ffixed-a1"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fmodules-ts"] = true,
            ["-mno-outline"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-mno-tgsplit"] = true,
            ["-fno-unroll-loops"] = true,
            ["-ffixed-x18"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fconvergent-functions"] = true,
            ["-fsycl"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["--version"] = true,
            ["-fno-profile-instr-generate"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["-v"] = true,
            ["-L"] = true,
            ["--no-seh"] = true,
            ["--allow-multiple-definition"] = true,
            ["--no-gc-sections"] = true,
            ["-static"] = true,
            ["--disable-nxcompat"] = true,
            ["--large-address-aware"] = true,
            ["--strip-debug"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--gc-sections"] = true,
            ["-dy"] = true,
            ["-S"] = true,
            ["--no-fatal-warnings"] = true,
            ["--verbose"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--version"] = true,
            ["--export-all-symbols"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["-s"] = true,
            ["--nxcompat"] = true,
            ["--no-demangle"] = true,
            ["--no-whole-archive"] = true,
            ["--insert-timestamp"] = true,
            ["--whole-archive"] = true,
            ["--enable-auto-import"] = true,
            ["--high-entropy-va"] = true,
            ["--help"] = true,
            ["--disable-dynamicbase"] = true,
            ["-o"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--disable-tsaware"] = true,
            ["--no-dynamicbase"] = true,
            ["--Bdynamic"] = true,
            ["-l"] = true,
            ["--demangle"] = true,
            ["--exclude-all-symbols"] = true,
            ["--strip-all"] = true,
            ["-dn"] = true,
            ["--appcontainer"] = true,
            ["--disable-auto-import"] = true,
            ["--no-insert-timestamp"] = true,
            ["--disable-no-seh"] = true,
            ["--shared"] = true,
            ["--dynamicbase"] = true,
            ["--kill-at"] = true,
            ["--fatal-warnings"] = true,
            ["-m"] = true,
            ["--Bstatic"] = true,
            ["--tsaware"] = true
        }
    }
}