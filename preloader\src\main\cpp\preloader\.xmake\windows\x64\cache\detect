{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Wno-gnu-line-marker -Werror"] = false,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-MMD -MF"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkver = "21",
            cross = "arm-linux-androideabi-",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            ndkver = 25
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program_fetch_package_system = {
        cmake = false
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-ffixed-x30"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fdata-sections"] = true,
            ["-b"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fuse-line-directives"] = true,
            ["-dsym-dir"] = true,
            ["-fcoverage-mapping"] = true,
            ["--migrate"] = true,
            ["-fms-extensions"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-iquote"] = true,
            ["-fasync-exceptions"] = true,
            ["-MD"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fexceptions"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fxray-instrument"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-pthread"] = true,
            ["-mhvx-qfloat"] = true,
            ["-MF"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-mgpopt"] = true,
            ["-fcall-saved-x11"] = true,
            ["-gdwarf-5"] = true,
            ["-nogpuinc"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-mthread-model"] = true,
            ["-mno-unaligned-access"] = true,
            ["-mno-tgsplit"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-ftime-trace"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-ffixed-x19"] = true,
            ["-fsanitize-stats"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-mabicalls"] = true,
            ["-funroll-loops"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fno-strict-return"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-fno-elide-type"] = true,
            ["-fstack-protector"] = true,
            ["-mmsa"] = true,
            ["-fsized-deallocation"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-fms-hotpatch"] = true,
            ["-fno-access-control"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-include-pch"] = true,
            ["-mlvi-hardening"] = true,
            ["-fno-temp-file"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fdigraphs"] = true,
            ["-ffinite-loops"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-nohipwrapperinc"] = true,
            ["-print-search-dirs"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fshort-enums"] = true,
            ["-gdwarf64"] = true,
            ["-mlong-double-128"] = true,
            ["-traditional-cpp"] = true,
            ["-nogpulib"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-cxx-modules"] = true,
            ["-femit-all-decls"] = true,
            ["-Qy"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fprofile-generate"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["--analyze"] = true,
            ["-mglobal-merge"] = true,
            ["-gno-embed-source"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fapplication-extension"] = true,
            ["-ffixed-x21"] = true,
            ["-ffixed-point"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-ffixed-d6"] = true,
            ["-fcf-protection"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fno-signed-char"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fembed-bitcode"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-mms-bitfields"] = true,
            ["-fgpu-rdc"] = true,
            ["-mrtd"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fdeclspec"] = true,
            ["-fno-debug-macro"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-ffixed-a6"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-ffixed-x1"] = true,
            ["-mno-mt"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-mstackrealign"] = true,
            ["-B"] = true,
            ["-fverbose-asm"] = true,
            ["-fms-compatibility"] = true,
            ["-mcmse"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fnew-infallible"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fcall-saved-x10"] = true,
            ["-static-libsan"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-mlong-double-64"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-print-resource-dir"] = true,
            ["-fstack-protector-all"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-mlocal-sdata"] = true,
            ["-MV"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-freg-struct-return"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-ffixed-x3"] = true,
            ["-mrecord-mcount"] = true,
            ["-mno-implicit-float"] = true,
            ["-fcxx-modules"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-mno-save-restore"] = true,
            ["-CC"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fno-unroll-loops"] = true,
            ["-mlong-calls"] = true,
            ["-fno-declspec"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-fno-fixed-point"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-MT"] = true,
            ["-MQ"] = true,
            ["-ffixed-x2"] = true,
            ["-fno-stack-protector"] = true,
            ["--gpu-bundle-output"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fwritable-strings"] = true,
            ["-faligned-allocation"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-mno-outline"] = true,
            ["-mno-neg-immediates"] = true,
            ["-x"] = true,
            ["-mmadd4"] = true,
            ["-dependency-file"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fstandalone-debug"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fstack-usage"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-emit-llvm"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fprotect-parens"] = true,
            ["-fblocks"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fno-operator-names"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-ftrapv"] = true,
            ["-mhvx"] = true,
            ["-fzvector"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fmerge-all-constants"] = true,
            ["-Xopenmp-target"] = true,
            ["-shared-libsan"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-trigraphs"] = true,
            ["-ffixed-x7"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-malign-double"] = true,
            ["-fno-plt"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-v"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-U"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fshort-wchar"] = true,
            ["-index-header-map"] = true,
            ["-iwithprefixbefore"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fdebug-types-section"] = true,
            ["-mmt"] = true,
            ["-mno-seses"] = true,
            ["-mno-embedded-data"] = true,
            ["-dI"] = true,
            ["-print-effective-triple"] = true,
            ["-mno-execute-only"] = true,
            ["-fgnu-keywords"] = true,
            ["-dM"] = true,
            ["--hip-link"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-msoft-float"] = true,
            ["-T"] = true,
            ["-finstrument-functions"] = true,
            ["-mstack-arg-probe"] = true,
            ["-ffixed-x9"] = true,
            ["-ivfsoverlay"] = true,
            ["-mexecute-only"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-fno-elide-constructors"] = true,
            ["-mrestrict-it"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fvectorize"] = true,
            ["-fkeep-static-consts"] = true,
            ["-msave-restore"] = true,
            ["-ffixed-x18"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-S"] = true,
            ["-ffixed-x11"] = true,
            ["--help-hidden"] = true,
            ["-mmemops"] = true,
            ["-ffixed-x5"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-mnvj"] = true,
            ["--precompile"] = true,
            ["-ffixed-a0"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-ftrigraphs"] = true,
            ["--emit-static-lib"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-meabi"] = true,
            ["-print-supported-cpus"] = true,
            ["-freciprocal-math"] = true,
            ["-femulated-tls"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-working-directory"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fopenmp"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-fno-show-source-location"] = true,
            ["-mno-madd4"] = true,
            ["-membedded-data"] = true,
            ["-fno-new-infallible"] = true,
            ["-fglobal-isel"] = true,
            ["-ffixed-x4"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-ffunction-sections"] = true,
            ["-mpackets"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-cxx-isystem"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fno-standalone-debug"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fno-common"] = true,
            ["-ffixed-x23"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-ffixed-a3"] = true,
            ["-freroll-loops"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-help"] = true,
            ["-save-temps"] = true,
            ["-foffload-lto"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-mno-nvj"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-P"] = true,
            ["-Xclang"] = true,
            ["-iwithprefix"] = true,
            ["-ffixed-x12"] = true,
            ["-mbackchain"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mno-relax"] = true,
            ["-ffixed-x16"] = true,
            ["-o"] = true,
            ["-ffixed-a1"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-save-stats"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fintegrated-as"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["--verify-debug-info"] = true,
            ["-fno-show-column"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-fsycl"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fno-digraphs"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-ffixed-x13"] = true,
            ["-fno-trigraphs"] = true,
            ["-fgnu89-inline"] = true,
            ["-fmath-errno"] = true,
            ["-relocatable-pch"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-ffixed-r19"] = true,
            ["-fsplit-stack"] = true,
            ["-ffast-math"] = true,
            ["-dependency-dot"] = true,
            ["-fjump-tables"] = true,
            ["-fno-short-wchar"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fno-pch-codegen"] = true,
            ["-mseses"] = true,
            ["-fpch-codegen"] = true,
            ["-ffixed-d3"] = true,
            ["-Xassembler"] = true,
            ["-fno-rtti-data"] = true,
            ["-M"] = true,
            ["-ffixed-a2"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fno-spell-checking"] = true,
            ["-ffixed-x29"] = true,
            ["-fno-exceptions"] = true,
            ["-w"] = true,
            ["-maix-struct-return"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-gcodeview"] = true,
            ["-mmark-bti-property"] = true,
            ["-ffixed-d5"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fstrict-enums"] = true,
            ["-gdwarf"] = true,
            ["-mextern-sdata"] = true,
            ["-fno-lto"] = true,
            ["-moutline-atomics"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-Xanalyzer"] = true,
            ["-Qunused-arguments"] = true,
            ["-isystem-after"] = true,
            ["-fcommon"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-mno-msa"] = true,
            ["-fpcc-struct-return"] = true,
            ["-L"] = true,
            ["-fno-split-stack"] = true,
            ["-fno-profile-generate"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-Xlinker"] = true,
            ["-ffixed-d7"] = true,
            ["-ffixed-x27"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fxray-link-deps"] = true,
            ["-module-file-info"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-undef"] = true,
            ["-mno-code-object-v3"] = true,
            ["-fgnu-runtime"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fmodules-decluse"] = true,
            ["-imacros"] = true,
            ["-fno-rtti"] = true,
            ["-fmodules-search-all"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-print-targets"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-moutline"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-mno-cumode"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fpascal-strings"] = true,
            ["-ffreestanding"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-nobuiltininc"] = true,
            ["-ffixed-x8"] = true,
            ["-nostdinc"] = true,
            ["-print-multiarch"] = true,
            ["-D"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-fropi"] = true,
            ["-static-openmp"] = true,
            ["-ffixed-x6"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-print-ivar-layout"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fno-addrsig"] = true,
            ["-fno-sycl"] = true,
            ["-pg"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-mno-crc"] = true,
            ["-munaligned-access"] = true,
            ["-fborland-extensions"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["--no-cuda-version-check"] = true,
            ["-gline-directives-only"] = true,
            ["-c"] = true,
            ["-z"] = true,
            ["-gcodeview-ghash"] = true,
            ["-isysroot"] = true,
            ["-fno-memory-profile"] = true,
            ["-mlong-double-80"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-MP"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fcall-saved-x14"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-fmemory-profile"] = true,
            ["-gline-tables-only"] = true,
            ["-I-"] = true,
            ["-fapple-kext"] = true,
            ["-verify-pch"] = true,
            ["-ffixed-x25"] = true,
            ["-mnop-mcount"] = true,
            ["-cl-opt-disable"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-mtgsplit"] = true,
            ["-mlvi-cfi"] = true,
            ["-mno-abicalls"] = true,
            ["-fenable-matrix"] = true,
            ["-ffixed-r9"] = true,
            ["-mno-outline-atomics"] = true,
            ["-mcrc"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-isystem"] = true,
            ["-finline-hint-functions"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-mno-packets"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-ffixed-x22"] = true,
            ["-cl-mad-enable"] = true,
            ["-ffixed-a4"] = true,
            ["-mcode-object-v3"] = true,
            ["-fcall-saved-x12"] = true,
            ["-fignore-exceptions"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-mno-movt"] = true,
            ["-Ttext"] = true,
            ["-I"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-ffixed-x10"] = true,
            ["-fpch-debuginfo"] = true,
            ["--analyzer-output"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-mno-gpopt"] = true,
            ["-ffixed-x31"] = true,
            ["-fapprox-func"] = true,
            ["-emit-merged-ifs"] = true,
            ["-print-target-triple"] = true,
            ["-fcall-saved-x8"] = true,
            ["-idirafter"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-ffixed-x17"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-fno-builtin"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fmodules"] = true,
            ["-gembed-source"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-g"] = true,
            ["-gdwarf32"] = true,
            ["-pedantic"] = true,
            ["-mpacked-stack"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fno-finite-loops"] = true,
            ["-fcall-saved-x13"] = true,
            ["-H"] = true,
            ["-fmodules-ts"] = true,
            ["-gdwarf-4"] = true,
            ["-cl-finite-math-only"] = true,
            ["-ffixed-d1"] = true,
            ["-fno-integrated-as"] = true,
            ["-ffixed-x14"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mfp64"] = true,
            ["-ffixed-x15"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-mnocrc"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fno-keep-static-consts"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-frwpi"] = true,
            ["-Wdeprecated"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fcs-profile-generate"] = true,
            ["-fobjc-exceptions"] = true,
            ["-gmodules"] = true,
            ["-ffixed-x24"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fconvergent-functions"] = true,
            ["-fintegrated-cc1"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-mfentry"] = true,
            ["-mcumode"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-G"] = true,
            ["-Qn"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-gdwarf-2"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-F"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fdebug-macro"] = true,
            ["-fsave-optimization-record"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fxray-ignore-loops"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-extract-api"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-include"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-Xpreprocessor"] = true,
            ["-ffixed-a5"] = true,
            ["-fslp-vectorize"] = true,
            ["-MJ"] = true,
            ["-fsystem-module"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-fdiscard-value-names"] = true,
            ["-MMD"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-faddrsig"] = true,
            ["-flto"] = true,
            ["-fobjc-weak"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fgpu-sanitize"] = true,
            ["-iprefix"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fno-offload-lto"] = true,
            ["-Tdata"] = true,
            ["-gdwarf-3"] = true,
            ["-time"] = true,
            ["-mno-hvx"] = true,
            ["-serialize-diagnostics"] = true,
            ["-mnvs"] = true,
            ["-fno-jump-tables"] = true,
            ["-fopenmp-simd"] = true,
            ["--cuda-host-only"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-mrelax"] = true,
            ["-MM"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fstack-size-section"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-ibuiltininc"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["--version"] = true,
            ["-ffixed-d4"] = true,
            ["-mno-restrict-it"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-use-init-array"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-MG"] = true,
            ["-E"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-mllvm"] = true,
            ["-emit-ast"] = true,
            ["-mno-nvs"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-mfp32"] = true,
            ["-fstack-protector-strong"] = true,
            ["-print-runtime-dir"] = true,
            ["-ffixed-d0"] = true,
            ["-finline-functions"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-module-dependency-dir"] = true,
            ["-miamcu"] = true,
            ["-fobjc-arc"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-fseh-exceptions"] = true,
            ["-fno-autolink"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fsigned-char"] = true,
            ["-mno-long-calls"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-mno-global-merge"] = true,
            ["-Tbss"] = true,
            ["-fcoroutines-ts"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-mqdsp6-compat"] = true,
            ["-emit-module"] = true,
            ["-ffixed-d2"] = true,
            ["-mno-extern-sdata"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["--cuda-device-only"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fno-global-isel"] = true,
            ["-iwithsysroot"] = true,
            ["-dD"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-ffixed-x20"] = true,
            ["-ffixed-x28"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-ffixed-x26"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fcolor-diagnostics"] = true,
            ["--config"] = true,
            ["-mibt-seal"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-pipe"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-mno-memops"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fsanitize-trap"] = true,
            ["-mrelax-all"] = true,
            ["-fforce-enable-int128"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-rpath"] = true,
            ["-C"] = true,
            ["-arch"] = true
        }
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    find_program_fetch_package_xmake = {
        cmake = false,
        ninja = false
    },
    find_program = {
        gzip = [[C:\msys64\usr\bin\gzip.exe]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        git = [[C:\Program Files\Git\cmd\git.exe]],
        tar = [[C:\Windows\System32\tar.exe]],
        clang = false,
        nim = false,
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        zig = false
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            version = "v3.11.3",
            license = "MIT",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            }
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            version = "10.2.1",
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            static = true,
            license = "MIT",
            links = {
                "fmt"
            }
        }
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--no-seh"] = true,
            ["--Bdynamic"] = true,
            ["--no-demangle"] = true,
            ["--version"] = true,
            ["--no-whole-archive"] = true,
            ["-dn"] = true,
            ["--disable-tsaware"] = true,
            ["--disable-auto-import"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["-L"] = true,
            ["--high-entropy-va"] = true,
            ["--strip-all"] = true,
            ["--appcontainer"] = true,
            ["--demangle"] = true,
            ["-v"] = true,
            ["--verbose"] = true,
            ["--large-address-aware"] = true,
            ["--no-fatal-warnings"] = true,
            ["--kill-at"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--disable-dynamicbase"] = true,
            ["-S"] = true,
            ["--dynamicbase"] = true,
            ["--insert-timestamp"] = true,
            ["--disable-nxcompat"] = true,
            ["--no-gc-sections"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--gc-sections"] = true,
            ["--exclude-all-symbols"] = true,
            ["--Bstatic"] = true,
            ["--nxcompat"] = true,
            ["--export-all-symbols"] = true,
            ["--whole-archive"] = true,
            ["-static"] = true,
            ["--enable-auto-import"] = true,
            ["--no-dynamicbase"] = true,
            ["--shared"] = true,
            ["-m"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["-o"] = true,
            ["-dy"] = true,
            ["--disable-no-seh"] = true,
            ["--no-insert-timestamp"] = true,
            ["-l"] = true,
            ["-s"] = true,
            ["--allow-multiple-definition"] = true,
            ["--tsaware"] = true,
            ["--fatal-warnings"] = true,
            ["--help"] = true,
            ["--strip-debug"] = true
        }
    }
}