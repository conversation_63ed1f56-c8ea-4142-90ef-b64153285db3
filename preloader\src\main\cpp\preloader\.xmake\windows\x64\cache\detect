{
    find_program = {
        tar = [[C:\Program Files\Git\usr\bin\tar.exe]],
        clang = false,
        git = [[C:\Program Files\Git\mingw64\bin\git.exe]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        gcc = false,
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        gzip = [[C:\Program Files\Git\usr\bin\gzip.exe]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-L"] = true,
            ["--no-fatal-warnings"] = true,
            ["--kill-at"] = true,
            ["--disable-dynamicbase"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--strip-all"] = true,
            ["-m"] = true,
            ["--disable-tsaware"] = true,
            ["-o"] = true,
            ["-s"] = true,
            ["--no-dynamicbase"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--allow-multiple-definition"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--Bdynamic"] = true,
            ["--no-seh"] = true,
            ["--no-whole-archive"] = true,
            ["-l"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--Bstatic"] = true,
            ["-v"] = true,
            ["-dn"] = true,
            ["--export-all-symbols"] = true,
            ["--no-insert-timestamp"] = true,
            ["-S"] = true,
            ["-dy"] = true,
            ["--verbose"] = true,
            ["--gc-sections"] = true,
            ["--strip-debug"] = true,
            ["--help"] = true,
            ["--nxcompat"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--enable-auto-import"] = true,
            ["--shared"] = true,
            ["--disable-auto-import"] = true,
            ["--no-demangle"] = true,
            ["--tsaware"] = true,
            ["--demangle"] = true,
            ["--insert-timestamp"] = true,
            ["--disable-no-seh"] = true,
            ["--exclude-all-symbols"] = true,
            ["--no-gc-sections"] = true,
            ["-static"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--version"] = true,
            ["--fatal-warnings"] = true,
            ["--large-address-aware"] = true,
            ["--dynamicbase"] = true,
            ["--high-entropy-va"] = true,
            ["--disable-nxcompat"] = true,
            ["--appcontainer"] = true,
            ["--whole-archive"] = true
        }
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            license = "MIT",
            version = "10.2.1",
            static = true,
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            links = {
                "fmt"
            }
        },
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            version = "v3.11.3",
            license = "MIT"
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fforce-enable-int128"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-ffixed-x21"] = true,
            ["-b"] = true,
            ["-cl-opt-disable"] = true,
            ["-mhvx"] = true,
            ["-ffixed-x11"] = true,
            ["-fno-builtin"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-nobuiltininc"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-fno-addrsig"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-MJ"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fcall-saved-x8"] = true,
            ["-E"] = true,
            ["-fsized-deallocation"] = true,
            ["-mllvm"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fno-elide-type"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fignore-exceptions"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-B"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-fdebug-macro"] = true,
            ["-cxx-isystem"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fdigraphs"] = true,
            ["-static-openmp"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fopenmp-simd"] = true,
            ["-MM"] = true,
            ["-fxray-link-deps"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-mcumode"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-C"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-ffinite-loops"] = true,
            ["-g"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-ffixed-x25"] = true,
            ["-freg-struct-return"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-print-effective-triple"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-fmodules-decluse"] = true,
            ["-fpascal-strings"] = true,
            ["-ffixed-x23"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["--verify-debug-info"] = true,
            ["-fno-profile-generate"] = true,
            ["-mlocal-sdata"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-ffixed-x26"] = true,
            ["-fintegrated-as"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fstrict-enums"] = true,
            ["-ffixed-x12"] = true,
            ["-ffixed-x14"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fgnu-keywords"] = true,
            ["-gdwarf-2"] = true,
            ["-fpch-debuginfo"] = true,
            ["-print-target-triple"] = true,
            ["-fno-access-control"] = true,
            ["-fmodules-search-all"] = true,
            ["-I"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-gline-tables-only"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-iprefix"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fno-operator-names"] = true,
            ["--cuda-device-only"] = true,
            ["-Xassembler"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-mms-bitfields"] = true,
            ["-index-header-map"] = true,
            ["-trigraphs"] = true,
            ["-Xpreprocessor"] = true,
            ["-fno-plt"] = true,
            ["-msave-restore"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-emit-module"] = true,
            ["-gdwarf-3"] = true,
            ["-mno-msa"] = true,
            ["-ffixed-x2"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-mno-cumode"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mmt"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fms-extensions"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-mno-local-sdata"] = true,
            ["-G"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-rpath"] = true,
            ["-iwithprefixbefore"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-fopenmp"] = true,
            ["-fmodules-ts"] = true,
            ["-print-resource-dir"] = true,
            ["-mnvj"] = true,
            ["-fdata-sections"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-fslp-vectorize"] = true,
            ["-mno-neg-immediates"] = true,
            ["-foffload-lto"] = true,
            ["-femit-all-decls"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-fstack-protector-all"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-dM"] = true,
            ["-verify-pch"] = true,
            ["-faddrsig"] = true,
            ["--no-cuda-version-check"] = true,
            ["-gno-embed-source"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-flto"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-MG"] = true,
            ["-ffixed-a0"] = true,
            ["-M"] = true,
            ["-fcf-protection"] = true,
            ["-faligned-allocation"] = true,
            ["--config"] = true,
            ["-mno-movt"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-Tbss"] = true,
            ["-mno-save-restore"] = true,
            ["-funroll-loops"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-ffixed-d3"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-elide-constructors"] = true,
            ["-fno-trigraphs"] = true,
            ["-fborland-extensions"] = true,
            ["-L"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fobjc-arc"] = true,
            ["-o"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fstack-protector"] = true,
            ["-ffixed-d2"] = true,
            ["-ftime-trace"] = true,
            ["-fstandalone-debug"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fms-compatibility"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-mmark-bti-property"] = true,
            ["-Xclang"] = true,
            ["-help"] = true,
            ["-mno-code-object-v3"] = true,
            ["-malign-double"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["--version"] = true,
            ["-mexecute-only"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-mfentry"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mno-abicalls"] = true,
            ["-Tdata"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-nogpuinc"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-Qunused-arguments"] = true,
            ["-mpackets"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-mlong-calls"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-mrtd"] = true,
            ["-fsplit-stack"] = true,
            ["-fgpu-sanitize"] = true,
            ["-mstackrealign"] = true,
            ["-ffixed-a2"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-dependency-dot"] = true,
            ["-mno-restrict-it"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-mcmse"] = true,
            ["-fno-standalone-debug"] = true,
            ["--analyzer-output"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-undef"] = true,
            ["-fno-pch-codegen"] = true,
            ["-P"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-pedantic"] = true,
            ["-pthread"] = true,
            ["--help-hidden"] = true,
            ["-gmodules"] = true,
            ["-fsanitize-trap"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-I-"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-mbackchain"] = true,
            ["--gpu-bundle-output"] = true,
            ["-mno-relax"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-S"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["--cuda-host-only"] = true,
            ["-fzvector"] = true,
            ["-emit-ast"] = true,
            ["-ffixed-d5"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fasync-exceptions"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-ffixed-d6"] = true,
            ["-mnvs"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-print-search-dirs"] = true,
            ["-fcs-profile-generate"] = true,
            ["-MD"] = true,
            ["-fapprox-func"] = true,
            ["-mtgsplit"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-mmemops"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fno-xray-function-index"] = true,
            ["-mwavefrontsize64"] = true,
            ["-mglobal-merge"] = true,
            ["-mno-implicit-float"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-U"] = true,
            ["-ffixed-d7"] = true,
            ["-ffixed-x7"] = true,
            ["-miamcu"] = true,
            ["-fno-global-isel"] = true,
            ["-ffixed-x16"] = true,
            ["-fdeclspec"] = true,
            ["-ffixed-x17"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-lto"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-pipe"] = true,
            ["-w"] = true,
            ["-fnew-infallible"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-ffixed-d1"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-serialize-diagnostics"] = true,
            ["-T"] = true,
            ["-freroll-loops"] = true,
            ["-fno-split-stack"] = true,
            ["-nostdinc"] = true,
            ["-mno-unaligned-access"] = true,
            ["-mmadd4"] = true,
            ["-mlvi-cfi"] = true,
            ["-ffixed-x27"] = true,
            ["-ffunction-sections"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fno-debug-macro"] = true,
            ["-fno-new-infallible"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-mgpopt"] = true,
            ["-mno-global-merge"] = true,
            ["-mno-embedded-data"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-mlong-double-80"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-print-ivar-layout"] = true,
            ["-mno-hvx"] = true,
            ["-ffixed-x20"] = true,
            ["-fno-exceptions"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fno-autolink"] = true,
            ["-gdwarf32"] = true,
            ["-gdwarf64"] = true,
            ["-fmemory-profile"] = true,
            ["-ivfsoverlay"] = true,
            ["-fno-unroll-loops"] = true,
            ["-mrecord-mcount"] = true,
            ["-fsplit-machine-functions"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["--emit-static-lib"] = true,
            ["-mno-tgsplit"] = true,
            ["-v"] = true,
            ["-extract-api"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-isystem-after"] = true,
            ["-fapplication-extension"] = true,
            ["-D"] = true,
            ["-fno-strict-return"] = true,
            ["-fno-signed-zeros"] = true,
            ["-ffixed-x19"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-show-column"] = true,
            ["-fno-use-init-array"] = true,
            ["-fno-keep-static-consts"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-dsym-dir"] = true,
            ["-fpcc-struct-return"] = true,
            ["-mlong-double-128"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-iwithprefix"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-ibuiltininc"] = true,
            ["-fno-sycl"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-femulated-tls"] = true,
            ["-cl-no-stdinc"] = true,
            ["-mlvi-hardening"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fms-hotpatch"] = true,
            ["-mno-outline-atomics"] = true,
            ["-mno-outline"] = true,
            ["-fno-integrated-as"] = true,
            ["-imacros"] = true,
            ["-gdwarf-4"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fcxx-exceptions"] = true,
            ["-F"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-mmsa"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fdebug-types-section"] = true,
            ["-mnop-mcount"] = true,
            ["-fxray-instrument"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-ffixed-x4"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-MF"] = true,
            ["-ffixed-d4"] = true,
            ["-ffixed-d0"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fcommon"] = true,
            ["-mrelax"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fmodules"] = true,
            ["-ffixed-x3"] = true,
            ["-Xopenmp-target"] = true,
            ["-x"] = true,
            ["-msoft-float"] = true,
            ["-fgnu89-inline"] = true,
            ["-mcrc"] = true,
            ["-fno-show-source-location"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-cl-mad-enable"] = true,
            ["-mqdsp6-compat"] = true,
            ["-mnocrc"] = true,
            ["--precompile"] = true,
            ["-pg"] = true,
            ["-mseses"] = true,
            ["-iwithsysroot"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-munaligned-access"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-fwritable-strings"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-mpacked-stack"] = true,
            ["-fshort-wchar"] = true,
            ["-fropi"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fseh-exceptions"] = true,
            ["-fmath-errno"] = true,
            ["-ffixed-a1"] = true,
            ["-fsigned-char"] = true,
            ["-mlong-double-64"] = true,
            ["-mthread-model"] = true,
            ["-ffixed-point"] = true,
            ["-arch"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-x28"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fjump-tables"] = true,
            ["-fno-offload-lto"] = true,
            ["-fgnu-runtime"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["--hip-link"] = true,
            ["-nohipwrapperinc"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-ffreestanding"] = true,
            ["-include-pch"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-ffixed-x30"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fprotect-parens"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-fvectorize"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-frwpi"] = true,
            ["-maix-struct-return"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fpch-codegen"] = true,
            ["-print-multiarch"] = true,
            ["-fstack-size-section"] = true,
            ["-ffixed-r9"] = true,
            ["-fobjc-exceptions"] = true,
            ["-z"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-save-stats"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-ffixed-x18"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fno-stack-protector"] = true,
            ["-fno-spell-checking"] = true,
            ["-fno-signed-char"] = true,
            ["-fno-short-wchar"] = true,
            ["-Ttext"] = true,
            ["-fno-declspec"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-working-directory"] = true,
            ["-mno-gpopt"] = true,
            ["-ffixed-x1"] = true,
            ["-mno-nvs"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-mabicalls"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-include"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-isystem"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-membedded-data"] = true,
            ["-fsystem-module"] = true,
            ["-fexceptions"] = true,
            ["-fcall-saved-x14"] = true,
            ["-mibt-seal"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fstack-clash-protection"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-finstrument-functions"] = true,
            ["-fno-fixed-point"] = true,
            ["-mfp32"] = true,
            ["-gline-directives-only"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fno-finite-loops"] = true,
            ["-freciprocal-math"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-gembed-source"] = true,
            ["-fcxx-modules"] = true,
            ["-ffixed-a4"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fapple-link-rtlib"] = true,
            ["--analyze"] = true,
            ["-gcodeview"] = true,
            ["-ffixed-x6"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-Xlinker"] = true,
            ["-fconvergent-functions"] = true,
            ["-gdwarf-5"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fcall-saved-x12"] = true,
            ["-ffixed-x5"] = true,
            ["-fapple-kext"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-ffast-math"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fcoverage-mapping"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-ffixed-x29"] = true,
            ["-ffixed-x8"] = true,
            ["-mfp64"] = true,
            ["-save-temps"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-idirafter"] = true,
            ["-dependency-file"] = true,
            ["-ftrapv"] = true,
            ["-fno-unique-section-names"] = true,
            ["-ffixed-a3"] = true,
            ["-fsanitize-stats"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-mno-execute-only"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-meabi"] = true,
            ["-MMD"] = true,
            ["-H"] = true,
            ["-module-dependency-dir"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-fcall-saved-x13"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-finline-functions"] = true,
            ["-finline-hint-functions"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fenable-matrix"] = true,
            ["-fembed-bitcode"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-print-targets"] = true,
            ["-fglobal-isel"] = true,
            ["-ffixed-x31"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-ffixed-x15"] = true,
            ["-ffixed-x9"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-CC"] = true,
            ["-ffixed-x22"] = true,
            ["-emit-llvm"] = true,
            ["-fshort-enums"] = true,
            ["-ffixed-a6"] = true,
            ["-dI"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-isysroot"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-time"] = true,
            ["-fuse-line-directives"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-gcodeview-ghash"] = true,
            ["-ffixed-x13"] = true,
            ["-dD"] = true,
            ["-gdwarf"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fno-jump-tables"] = true,
            ["-fblocks"] = true,
            ["-fstack-usage"] = true,
            ["-mrelax-all"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-iquote"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-fsycl"] = true,
            ["-ffixed-x24"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-fno-digraphs"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-module-file-info"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fcall-saved-x18"] = true,
            ["-Xanalyzer"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-Wdeprecated"] = true,
            ["-fno-rtti"] = true,
            ["-mno-nvj"] = true,
            ["-traditional-cpp"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-static-libsan"] = true,
            ["-shared-libsan"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fno-temp-file"] = true,
            ["-mno-crc"] = true,
            ["-rewrite-objc"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-mcode-object-v3"] = true,
            ["-MP"] = true,
            ["-relocatable-pch"] = true,
            ["-Qy"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-cl-finite-math-only"] = true,
            ["-print-supported-cpus"] = true,
            ["-MV"] = true,
            ["-print-runtime-dir"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-mrestrict-it"] = true,
            ["-ffixed-x10"] = true,
            ["-nogpulib"] = true,
            ["-fsave-optimization-record"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-rtti-data"] = true,
            ["-moutline-atomics"] = true,
            ["-mno-madd4"] = true,
            ["-fcall-saved-x11"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-MQ"] = true,
            ["-fminimize-whitespace"] = true,
            ["-fintegrated-cc1"] = true,
            ["-mno-memops"] = true,
            ["-fprofile-generate"] = true,
            ["-fno-common"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-moutline"] = true,
            ["-c"] = true,
            ["-Qn"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-mextern-sdata"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fgpu-rdc"] = true,
            ["--migrate"] = true,
            ["-fobjc-weak"] = true,
            ["-mno-seses"] = true,
            ["-mno-packets"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-MT"] = true,
            ["-fverbose-asm"] = true,
            ["-mno-mt"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-mno-long-calls"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-ffixed-r19"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fhip-new-launch-api"] = true
        }
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            ndkver = 25,
            cross = "arm-linux-androideabi-",
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkver = "21",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    }
}