{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fno-cuda-approx-transcendentals"] = true,
            ["--cuda-noopt-device-debug"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-mlocal-sdata"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-L"] = true,
            ["--analyze"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-ffixed-x29"] = true,
            ["-gdwarf32"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-mibt-seal"] = true,
            ["-freroll-loops"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fobjc-weak"] = true,
            ["-fnew-infallible"] = true,
            ["-msave-restore"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-ffixed-x17"] = true,
            ["-static-libsan"] = true,
            ["-fcall-saved-x8"] = true,
            ["-trigraphs"] = true,
            ["-ffixed-x16"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["--cuda-device-only"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-fapprox-func"] = true,
            ["-mmadd4"] = true,
            ["-gmodules"] = true,
            ["-fdiscard-value-names"] = true,
            ["-fno-operator-names"] = true,
            ["-ffixed-x13"] = true,
            ["-fexceptions"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fno-standalone-debug"] = true,
            ["--precompile"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-fforce-enable-int128"] = true,
            ["-gdwarf-2"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-mglobal-merge"] = true,
            ["-mno-gpopt"] = true,
            ["-ffixed-x31"] = true,
            ["-fno-unique-section-names"] = true,
            ["-frwpi"] = true,
            ["-fms-compatibility"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fopenmp"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-mms-bitfields"] = true,
            ["-fenable-matrix"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mabicalls"] = true,
            ["-Qunused-arguments"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-Xopenmp-target"] = true,
            ["-mnvj"] = true,
            ["-print-multiarch"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-ffixed-a5"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-meabi"] = true,
            ["-fno-profile-generate"] = true,
            ["-fjump-tables"] = true,
            ["-fopenmp-extensions"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-iquote"] = true,
            ["-fsigned-char"] = true,
            ["--analyzer-output"] = true,
            ["-fstrict-enums"] = true,
            ["-fno-exceptions"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fstandalone-debug"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fcall-saved-x13"] = true,
            ["-undef"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fno-cxx-modules"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fno-declspec"] = true,
            ["-fno-global-isel"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fuse-line-directives"] = true,
            ["-fsave-optimization-record"] = true,
            ["-MP"] = true,
            ["-ffixed-r9"] = true,
            ["-mllvm"] = true,
            ["-mno-neg-immediates"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-fno-rtti-data"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fmerge-all-constants"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-pg"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-ffixed-x10"] = true,
            ["-ibuiltininc"] = true,
            ["-ffixed-a1"] = true,
            ["-dsym-dir"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fpascal-strings"] = true,
            ["-fvectorize"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fdata-sections"] = true,
            ["-fgpu-rdc"] = true,
            ["-fno-autolink"] = true,
            ["-mexecute-only"] = true,
            ["-mno-outline-atomics"] = true,
            ["-mnocrc"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["-fno-lto"] = true,
            ["-include-pch"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-MMD"] = true,
            ["-mmemops"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-T"] = true,
            ["-fgpu-sanitize"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-static-openmp"] = true,
            ["-extract-api"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-index-header-map"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-gembed-source"] = true,
            ["-fembed-bitcode"] = true,
            ["-mno-msa"] = true,
            ["-idirafter"] = true,
            ["-time"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-print-effective-triple"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-ffixed-x25"] = true,
            ["-mpacked-stack"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fborland-extensions"] = true,
            ["-mno-embedded-data"] = true,
            ["-fdebug-macro"] = true,
            ["-mlong-double-128"] = true,
            ["-mno-relax"] = true,
            ["-fshort-wchar"] = true,
            ["-fno-new-infallible"] = true,
            ["-fcoroutines-ts"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-print-supported-cpus"] = true,
            ["-isystem"] = true,
            ["-fcxx-exceptions"] = true,
            ["-ffixed-x22"] = true,
            ["-fmodules-search-all"] = true,
            ["-ffixed-x7"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-mno-execute-only"] = true,
            ["-ffixed-x18"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-mno-code-object-v3"] = true,
            ["-finstrument-functions"] = true,
            ["-fxray-instrument"] = true,
            ["-mmark-bti-property"] = true,
            ["-S"] = true,
            ["-ffixed-x21"] = true,
            ["-ffixed-a4"] = true,
            ["-emit-llvm"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-ffixed-x15"] = true,
            ["-mlong-double-80"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-gdwarf-5"] = true,
            ["-include"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-mno-restrict-it"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-fconvergent-functions"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fprofile-generate"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-mno-seses"] = true,
            ["-fno-signed-zeros"] = true,
            ["-nogpulib"] = true,
            ["-mrelax"] = true,
            ["-ffixed-a3"] = true,
            ["-U"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fmodules"] = true,
            ["-ftrapv"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-ffixed-x4"] = true,
            ["-fsplit-stack"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-mno-madd4"] = true,
            ["-mcode-object-v3"] = true,
            ["--config"] = true,
            ["-nostdinc"] = true,
            ["-gno-embed-source"] = true,
            ["-working-directory"] = true,
            ["-nobuiltininc"] = true,
            ["-fno-trigraphs"] = true,
            ["-print-target-triple"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-dI"] = true,
            ["-b"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-mhvx"] = true,
            ["-fno-digraphs"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-gline-directives-only"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-ffunction-sections"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-munaligned-access"] = true,
            ["-B"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fstack-size-section"] = true,
            ["-fcxx-modules"] = true,
            ["-msoft-float"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-pipe"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-fno-stack-protector"] = true,
            ["-fno-elide-type"] = true,
            ["-fno-use-init-array"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-fno-finite-loops"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-fasync-exceptions"] = true,
            ["-objcmt-atomic-property"] = true,
            ["--hip-link"] = true,
            ["-fsycl"] = true,
            ["-mfp64"] = true,
            ["--verify-debug-info"] = true,
            ["-nohipwrapperinc"] = true,
            ["-nogpuinc"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-ffixed-x12"] = true,
            ["-mrecord-mcount"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-ffixed-a0"] = true,
            ["-mno-local-sdata"] = true,
            ["-fno-offload-lto"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-moutline"] = true,
            ["-fno-show-source-location"] = true,
            ["-MM"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-shared-libsan"] = true,
            ["-MJ"] = true,
            ["-fprofile-sample-accurate"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fminimize-whitespace"] = true,
            ["-MV"] = true,
            ["-print-runtime-dir"] = true,
            ["-ffixed-x20"] = true,
            ["-mno-outline"] = true,
            ["-mlvi-hardening"] = true,
            ["-ffixed-x8"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-fno-short-wchar"] = true,
            ["-cl-mad-enable"] = true,
            ["-fms-extensions"] = true,
            ["-malign-double"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-ffixed-a2"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-ffixed-d3"] = true,
            ["-fcf-protection"] = true,
            ["-mcmse"] = true,
            ["-ffixed-a6"] = true,
            ["-dM"] = true,
            ["-rewrite-objc"] = true,
            ["-fno-sycl"] = true,
            ["-gcodeview"] = true,
            ["-finline-functions"] = true,
            ["-MQ"] = true,
            ["-fgnu-keywords"] = true,
            ["-moutline-atomics"] = true,
            ["-fmodules-ts"] = true,
            ["-help"] = true,
            ["-ftime-trace"] = true,
            ["--cuda-host-only"] = true,
            ["-freg-struct-return"] = true,
            ["-iwithprefix"] = true,
            ["-fprotect-parens"] = true,
            ["-fapplication-extension"] = true,
            ["-fstack-protector"] = true,
            ["-iprefix"] = true,
            ["-mtgsplit"] = true,
            ["-mseses"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-ffixed-x27"] = true,
            ["-H"] = true,
            ["-ffixed-d4"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mqdsp6-compat"] = true,
            ["-fno-addrsig"] = true,
            ["-ffixed-x19"] = true,
            ["-isystem-after"] = true,
            ["-fpcc-struct-return"] = true,
            ["-ffixed-x26"] = true,
            ["-M"] = true,
            ["-mno-global-merge"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fno-strict-return"] = true,
            ["-mno-movt"] = true,
            ["-mpackets"] = true,
            ["-CC"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-fno-integrated-as"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-v"] = true,
            ["-gline-tables-only"] = true,
            ["-ffixed-d6"] = true,
            ["-pthread"] = true,
            ["-rpath"] = true,
            ["-fgnu89-inline"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-gdwarf64"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-emit-module"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-cl-finite-math-only"] = true,
            ["-G"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fsanitize-stats"] = true,
            ["-mbackchain"] = true,
            ["-mrestrict-it"] = true,
            ["-mno-abicalls"] = true,
            ["-ffixed-x28"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-MT"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-fpch-debuginfo"] = true,
            ["-mstackrealign"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-femit-all-decls"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-funroll-loops"] = true,
            ["-ffixed-x5"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-mno-save-restore"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-mlong-double-64"] = true,
            ["-dependency-file"] = true,
            ["--migrate"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-fcs-profile-generate"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-Qn"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-fintegrated-cc1"] = true,
            ["-fno-access-control"] = true,
            ["-ffast-math"] = true,
            ["-fstack-protector-all"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-gcodeview-ghash"] = true,
            ["-fcoverage-mapping"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-ffixed-d2"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-faligned-allocation"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-fropi"] = true,
            ["-fseh-exceptions"] = true,
            ["-fsanitize-trap"] = true,
            ["-mno-packets"] = true,
            ["-gdwarf"] = true,
            ["-fshort-enums"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fcall-saved-x18"] = true,
            ["-g"] = true,
            ["-ffixed-x9"] = true,
            ["-fno-rtti"] = true,
            ["-relocatable-pch"] = true,
            ["-cl-no-stdinc"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-mlvi-cfi"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-fno-elide-constructors"] = true,
            ["-D"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-fxray-link-deps"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-fno-plt"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-fverbose-asm"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-fpch-codegen"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-save-temps"] = true,
            ["--version"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-flto"] = true,
            ["-cl-opt-disable"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-fgnu-runtime"] = true,
            ["-mfp32"] = true,
            ["-fms-hotpatch"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fmemory-profile"] = true,
            ["-arch"] = true,
            ["-fno-builtin"] = true,
            ["-fno-split-stack"] = true,
            ["-module-file-info"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-ffixed-x3"] = true,
            ["-MD"] = true,
            ["--emit-static-lib"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-fsized-deallocation"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-print-ivar-layout"] = true,
            ["-mno-implicit-float"] = true,
            ["-iwithprefixbefore"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fblocks"] = true,
            ["-fno-memory-profile"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-fansi-escape-codes"] = true,
            ["-Qy"] = true,
            ["-dependency-dot"] = true,
            ["-Xpreprocessor"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-fno-xray-function-index"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-z"] = true,
            ["-fstack-usage"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-MG"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-fno-jump-tables"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-isysroot"] = true,
            ["-fintegrated-as"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-fzvector"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fdeclspec"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-fapple-kext"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-freciprocal-math"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-mthread-model"] = true,
            ["-MF"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-ffixed-d7"] = true,
            ["-fopenmp-simd"] = true,
            ["-ffixed-d1"] = true,
            ["-mextern-sdata"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-mcrc"] = true,
            ["-C"] = true,
            ["-fmodules-decluse"] = true,
            ["-ffixed-x6"] = true,
            ["-mno-long-calls"] = true,
            ["-ffixed-d5"] = true,
            ["-pedantic"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-fno-common"] = true,
            ["-ffixed-x24"] = true,
            ["-femulated-tls"] = true,
            ["-fno-signed-char"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-mno-mt"] = true,
            ["-fno-show-column"] = true,
            ["-mno-hvx"] = true,
            ["-I"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-faddrsig"] = true,
            ["--help-hidden"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-gdwarf-4"] = true,
            ["-c"] = true,
            ["-fno-fixed-point"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fcall-saved-x11"] = true,
            ["-ffreestanding"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-x"] = true,
            ["-fno-spell-checking"] = true,
            ["-imacros"] = true,
            ["-F"] = true,
            ["-ffixed-x23"] = true,
            ["-emit-ast"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["-dD"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fignore-exceptions"] = true,
            ["-fobjc-arc"] = true,
            ["-foffload-lto"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-Xassembler"] = true,
            ["-fno-temp-file"] = true,
            ["-fcommon"] = true,
            ["-iwithsysroot"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-mfentry"] = true,
            ["-fbuiltin-module-map"] = true,
            ["--no-cuda-version-check"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fglobal-isel"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-fsystem-module"] = true,
            ["-print-resource-dir"] = true,
            ["-gdwarf-3"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-mno-nvs"] = true,
            ["-mnvs"] = true,
            ["-finline-hint-functions"] = true,
            ["-E"] = true,
            ["-ffixed-r19"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fslp-vectorize"] = true,
            ["-fmath-errno"] = true,
            ["-Xclang"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-traditional-cpp"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fdebug-types-section"] = true,
            ["-mlong-calls"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-mno-cumode"] = true,
            ["-mgpopt"] = true,
            ["-fcall-saved-x15"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-maix-struct-return"] = true,
            ["-mno-crc"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fwritable-strings"] = true,
            ["-mcumode"] = true,
            ["-verify-pch"] = true,
            ["-Xlinker"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fcall-saved-x14"] = true,
            ["-ffixed-point"] = true,
            ["-Xanalyzer"] = true,
            ["-Wdeprecated"] = true,
            ["-mrelax-all"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-Ttext"] = true,
            ["-I-"] = true,
            ["-Tdata"] = true,
            ["-Tbss"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-ffixed-d0"] = true,
            ["-fcall-saved-x12"] = true,
            ["-ffixed-x30"] = true,
            ["-save-stats"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-membedded-data"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-P"] = true,
            ["-print-targets"] = true,
            ["-print-search-dirs"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-fdigraphs"] = true,
            ["-o"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-module-dependency-dir"] = true,
            ["-fno-debug-macro"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-ffixed-x14"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-fkeep-static-consts"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-mmsa"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-mrtd"] = true,
            ["-fcall-saved-x9"] = true,
            ["-fstack-clash-protection"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-mno-tgsplit"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-ffixed-x11"] = true,
            ["-mmt"] = true,
            ["-mno-nvj"] = true,
            ["-ffixed-x1"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-w"] = true,
            ["-mnop-mcount"] = true,
            ["-miamcu"] = true,
            ["-ivfsoverlay"] = true,
            ["-mno-memops"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-cxx-isystem"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fobjc-exceptions"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-ftrigraphs"] = true,
            ["-fno-pch-codegen"] = true,
            ["-ffinite-loops"] = true,
            ["-ffixed-x2"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-fdiagnostics-print-source-range-info"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            version = "v3.11.3",
            license = "MIT"
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            license = "MIT",
            static = true,
            links = {
                "fmt"
            },
            version = "10.2.1",
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            }
        }
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-dn"] = true,
            ["--no-insert-timestamp"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--insert-timestamp"] = true,
            ["--strip-debug"] = true,
            ["-static"] = true,
            ["-L"] = true,
            ["--nxcompat"] = true,
            ["--appcontainer"] = true,
            ["--exclude-all-symbols"] = true,
            ["-m"] = true,
            ["--dynamicbase"] = true,
            ["-s"] = true,
            ["--Bstatic"] = true,
            ["--demangle"] = true,
            ["--high-entropy-va"] = true,
            ["--disable-no-seh"] = true,
            ["--disable-auto-import"] = true,
            ["--gc-sections"] = true,
            ["--tsaware"] = true,
            ["--no-allow-multiple-definition"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--export-all-symbols"] = true,
            ["--kill-at"] = true,
            ["--no-dynamicbase"] = true,
            ["--allow-multiple-definition"] = true,
            ["-dy"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["--version"] = true,
            ["--Bdynamic"] = true,
            ["-o"] = true,
            ["--shared"] = true,
            ["--no-seh"] = true,
            ["--help"] = true,
            ["--whole-archive"] = true,
            ["--enable-auto-import"] = true,
            ["--no-whole-archive"] = true,
            ["--no-gc-sections"] = true,
            ["--no-fatal-warnings"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--disable-nxcompat"] = true,
            ["-l"] = true,
            ["--disable-dynamicbase"] = true,
            ["--disable-tsaware"] = true,
            ["--fatal-warnings"] = true,
            ["-v"] = true,
            ["--verbose"] = true,
            ["--strip-all"] = true,
            ["-S"] = true,
            ["--large-address-aware"] = true,
            ["--no-demangle"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fvisibility-inlines-hidden"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            sdkver = "21",
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            ndkver = 25,
            cross = "arm-linux-androideabi-",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]]
        }
    },
    find_program = {
        gzip = [[C:\Program Files\Git\usr\bin\gzip.exe]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        gcc = false,
        tar = [[C:\Program Files\Git\usr\bin\tar.exe]],
        git = [[C:\Program Files\Git\mingw64\bin\git.exe]],
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]],
        clang = false
    }
}