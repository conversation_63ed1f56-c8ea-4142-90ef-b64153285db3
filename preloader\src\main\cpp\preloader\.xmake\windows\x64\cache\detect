{
    ["find_package_android_armeabi-v7a_fetch_package_xmake"] = {
        ["xmake::nlohmann_json_be0d1f3d98814d41bdf16f6957e7a2d6_release_v3.11.3_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\n\nlohmann_json\v3.11.3\be0d1f3d98814d41bdf16f6957e7a2d6\include]]
            },
            license = "MIT",
            version = "v3.11.3"
        },
        ["xmake::fmt_59a17d68dbe340a1a0c7e1c511c99e36_release_10.2.1_external"] = {
            sysincludedirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\include]]
            },
            version = "10.2.1",
            static = true,
            libfiles = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib\libfmt.a]]
            },
            linkdirs = {
                [[C:\Users\<USER>\AppData\Local\.xmake\packages\f\fmt\10.2.1\59a17d68dbe340a1a0c7e1c511c99e36\lib]]
            },
            license = "MIT",
            links = {
                "fmt"
            }
        }
    },
    find_program = {
        gzip = [[C:\Program Files\Git\usr\bin\gzip.exe]],
        git = [[C:\Program Files\Git\mingw64\bin\git.exe]],
        gcc = false,
        tar = [[C:\Program Files\Git\usr\bin\tar.exe]],
        ["vswhere.exe"] = [[C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe]],
        clang = false
    },
    find_program_fetch_package_xmake = {
        ninja = false,
        cmake = false
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            cross = "arm-linux-androideabi-",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
            ndkver = 25,
            sdkver = "21"
        }
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    }
}