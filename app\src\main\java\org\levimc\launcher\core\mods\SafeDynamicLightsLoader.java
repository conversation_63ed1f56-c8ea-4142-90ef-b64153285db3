package org.levimc.launcher.core.mods;

import org.levimc.launcher.util.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * SafeDynamicLightsLoader provides multiple strategies for safely loading DynamicLights
 * to prevent crashes that occur when the mod is loaded too early or under unsafe conditions.
 */
public class SafeDynamicLightsLoader {
    
    private static final AtomicBoolean isLoaded = new AtomicBoolean(false);
    private static final AtomicBoolean isLoading = new AtomicBoolean(false);
    private static final AtomicBoolean loadingFailed = new AtomicBoolean(false);
    
    /**
     * Attempts to load DynamicLights with maximum safety measures.
     * This method includes multiple fallback strategies and extensive error handling.
     * 
     * @return true if DynamicLights was loaded successfully, false otherwise
     */
    public static boolean loadDynamicLightsSafely() {
        if (isLoaded.get()) {
            Logger.get().info("DynamicLights is already loaded");
            return true;
        }
        
        if (isLoading.get()) {
            Logger.get().warn("DynamicLights is currently being loaded by another thread");
            return false;
        }
        
        if (loadingFailed.get()) {
            Logger.get().warn("DynamicLights loading previously failed, skipping retry");
            return false;
        }
        
        isLoading.set(true);
        
        try {
            Logger.get().info("Starting safe DynamicLights loading process...");
            
            // Strategy 1: Check if CrestHelper is available
            if (!ModBundledLoader.isBundledMod("CrestHelper")) {
                Logger.get().error("Cannot load DynamicLights: CrestHelper dependency not available");
                return false;
            }
            
            // Strategy 2: Add DynamicLights to configuration temporarily
            List<String> deps = new ArrayList<>();
            deps.add("CrestHelper");
            ModBundledLoader.addBundledMod("DynamicLights", deps);
            
            // Strategy 3: Wait for system to stabilize
            Logger.get().info("Waiting for system stabilization before loading DynamicLights...");
            Thread.sleep(2000); // 2 second stabilization period
            
            // Strategy 4: Load with extensive error handling
            Logger.get().info("Attempting to load DynamicLights library...");
            System.loadLibrary("DynamicLights");
            
            // Strategy 5: Verify loading was successful
            Thread.sleep(500); // Allow time for initialization
            
            isLoaded.set(true);
            Logger.get().info("DynamicLights loaded successfully with safe loading strategy!");
            return true;
            
        } catch (UnsatisfiedLinkError e) {
            Logger.get().error("UnsatisfiedLinkError loading DynamicLights: " + e.getMessage());
            Logger.get().error("This usually indicates a compatibility issue or missing dependencies");
            loadingFailed.set(true);
            return false;
            
        } catch (SecurityException e) {
            Logger.get().error("SecurityException loading DynamicLights: " + e.getMessage());
            Logger.get().error("This indicates a permission or security policy issue");
            loadingFailed.set(true);
            return false;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            Logger.get().warn("DynamicLights loading was interrupted");
            return false;
            
        } catch (Exception e) {
            Logger.get().error("Unexpected error loading DynamicLights: " + e.getMessage());
            Logger.get().error("Exception type: " + e.getClass().getSimpleName());
            loadingFailed.set(true);
            return false;
            
        } finally {
            isLoading.set(false);
        }
    }
    
    /**
     * Attempts to load DynamicLights with a progressive delay strategy.
     * This method tries multiple times with increasing delays between attempts.
     * 
     * @param maxAttempts Maximum number of loading attempts
     * @param baseDelayMs Base delay in milliseconds (will be multiplied by attempt number)
     */
    public static void loadDynamicLightsProgressive(int maxAttempts, long baseDelayMs) {
        new Thread(() -> {
            for (int attempt = 1; attempt <= maxAttempts; attempt++) {
                try {
                    long delay = baseDelayMs * attempt;
                    Logger.get().info("DynamicLights loading attempt " + attempt + "/" + maxAttempts + 
                                    " (delay: " + delay + "ms)");
                    
                    Thread.sleep(delay);
                    
                    if (loadDynamicLightsSafely()) {
                        Logger.get().info("DynamicLights loaded successfully on attempt " + attempt);
                        return;
                    }
                    
                    if (loadingFailed.get()) {
                        Logger.get().error("DynamicLights loading failed permanently, stopping attempts");
                        return;
                    }
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    Logger.get().warn("DynamicLights progressive loading was interrupted");
                    return;
                }
            }
            
            Logger.get().warn("DynamicLights loading failed after " + maxAttempts + " attempts");
        }).start();
    }
    
    /**
     * Checks if DynamicLights is currently loaded.
     * 
     * @return true if DynamicLights is loaded, false otherwise
     */
    public static boolean isDynamicLightsLoaded() {
        return isLoaded.get();
    }
    
    /**
     * Checks if DynamicLights loading has failed.
     * 
     * @return true if loading failed, false otherwise
     */
    public static boolean hasLoadingFailed() {
        return loadingFailed.get();
    }
    
    /**
     * Resets the loading state, allowing for retry attempts.
     * Use this method carefully, only when you're sure the conditions have changed.
     */
    public static void resetLoadingState() {
        isLoaded.set(false);
        isLoading.set(false);
        loadingFailed.set(false);
        Logger.get().info("DynamicLights loading state has been reset");
    }
    
    /**
     * Gets the current status of DynamicLights loading.
     * 
     * @return A descriptive string of the current status
     */
    public static String getLoadingStatus() {
        if (isLoaded.get()) {
            return "DynamicLights: LOADED";
        } else if (isLoading.get()) {
            return "DynamicLights: LOADING...";
        } else if (loadingFailed.get()) {
            return "DynamicLights: FAILED";
        } else {
            return "DynamicLights: NOT LOADED";
        }
    }
}
